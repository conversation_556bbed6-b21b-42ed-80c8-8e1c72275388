# Dual-Authentication Smart Attendance System

A comprehensive attendance management and exam eligibility verification system designed for INES Ruhengeri and other higher education institutions in Rwanda.

## 🚀 Features

### Core Functionality
- **Dual-Factor Authentication**: RFID + Biometric (fingerprint) verification
- **Class Attendance Tracking**: Entry/exit verification for complete attendance
- **Exam Eligibility Verification**: Automated eligibility based on attendance (≥80%) and payment status
- **Real-time Monitoring**: Live dashboard with attendance statistics and alerts
- **Multi-Modal Operation**: Seamless switching between class and exam modes

### System Capabilities
- **Student Management**: Registration, RFID assignment, fingerprint enrollment
- **Course Management**: Course setup, session scheduling, attendance thresholds
- **Payment Integration**: Fee verification for exam eligibility
- **Automated Notifications**: Email alerts for attendance warnings and exam eligibility
- **Comprehensive Reporting**: Detailed attendance reports and analytics
- **Role-Based Access Control**: Admin, Department, and Accountant dashboards

## 🏗️ System Architecture

```
┌─────────────────────────────────────────────────────┐
│                 Web Interface                        │
├─────────────────────────────────────────────────────┤
│  Admin Dashboard  │  Dept Dashboard  │  Accountant   │
└─────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────┐
│                 Backend API                         │
├─────────────────────────────────────────────────────┤
│  Authentication  │  Attendance  │  Exams  │ Reports │
└─────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────┐
│                 Database Layer                      │
├─────────────────────────────────────────────────────┤
│  Students  │  Attendance  │  Exams  │  Payments     │
└─────────────────────────────────────────────────────┘
                           │
┌─────────────────────────────────────────────────────┐
│              Hardware Devices                       │
├─────────────────────────────────────────────────────┤
│  ESP32 + RFID + Fingerprint + OLED Display          │
└─────────────────────────────────────────────────────┘
```

## 📋 Prerequisites

### Software Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.14+
- **PHP**: 7.4 or higher with extensions:
  - PDO MySQL
  - JSON
  - cURL
  - OpenSSL
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Modern Web Browser**: Chrome, Firefox, Safari, Edge

### Hardware Requirements
- **ESP32 Development Board**
- **RC522 RFID Reader Module**
- **R307 Fingerprint Scanner**
- **0.96" I2C OLED Display**
- **Buzzer and LEDs**
- **Power Supply (5V)**
- **Breadboard and Jumper Wires**

## 🔧 Installation

### Quick Setup (Recommended)

1. **Clone the Repository**
```bash
git clone https://github.com/your-repo/attendance-system.git
cd attendance-system
```

2. **Run Setup Script**
```bash
chmod +x setup.sh
./setup.sh
```

3. **Access the System**
- URL: `http://attendance-system.local`
- Default Login: `<EMAIL>` / `admin123`

### Manual Installation

1. **Database Setup**
```bash
mysql -u root -p
CREATE DATABASE attendance_system;
CREATE USER 'attendance_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON attendance_system.* TO 'attendance_user'@'localhost';
FLUSH PRIVILEGES;
```

2. **Import Database Schema**
```bash
mysql -u attendance_user -p attendance_system < backend/sql/attendance_system.sql
```

3. **Configure Database Connection**
Edit `backend/config/database.php`:
```php
private $host = 'localhost';
private $username = 'attendance_user';
private $password = 'secure_password';
private $database = 'attendance_system';
```

4. **Web Server Configuration**

**Apache (.htaccess)**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ backend/api/$1 [L]
```

**Nginx**
```nginx
server {
    listen 80;
    server_name attendance-system.local;
    root /var/www/attendance-system;
    index index.html;

    location /api/ {
        try_files $uri $uri/ /backend/api/$1;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

5. **Set Permissions**
```bash
chmod 755 backend/ frontend/
chmod 777 logs/ uploads/ backups/
```

## 🔌 Hardware Setup

### Circuit Connections

**ESP32 Pin Connections:**
```
ESP32          Component         Pin
GPIO21    -->  RC522 SDA    -->  SDA
GPIO22    -->  RC522 RST    -->  RST
GND       -->  RC522 GND    -->  GND
3.3V      -->  RC522 VCC    -->  VCC
GPIO23    -->  RC522 MOSI   -->  MOSI
GPIO19    -->  RC522 MISO   -->  MISO
GPIO18    -->  RC522 SCK    -->  SCK

GPIO16    -->  R307 TX      -->  RX
GPIO17    -->  R307 RX      -->  TX
5V        -->  R307 VCC     -->  VCC
GND       -->  R307 GND     -->  GND

GPIO2     -->  Buzzer       -->  +
GPIO4     -->  Green LED    -->  Anode
GPIO5     -->  Red LED      -->  Anode
GPIO0     -->  Button       -->  One Terminal

SDA(21)   -->  OLED SDA     -->  SDA
SCL(22)   -->  OLED SCL     -->  SCL
3.3V      -->  OLED VCC     -->  VCC
GND       -->  OLED GND     -->  GND
```

### Arduino IDE Setup

1. **Install ESP32 Board Support**
   - Open Arduino IDE
   - Go to File → Preferences
   - Add URL: `https://dl.espressif.com/dl/package_esp32_index.json`
   - Go to Tools → Board → Boards Manager
   - Search and install "ESP32"

2. **Install Required Libraries**
```
- MFRC522 by GithubCommunity
- Adafruit Fingerprint Sensor Library
- Adafruit SSD1306
- Adafruit GFX Library
- ArduinoJson by Benoit Blanchon
```

3. **Upload Code**
   - Open `hardware/arduino/main_controller/main_controller.ino`
   - Update WiFi credentials:
   ```cpp
   const char* ssid = "YOUR_WIFI_SSID";
   const char* password = "YOUR_WIFI_PASSWORD";
   const char* serverURL = "http://YOUR_SERVER_IP/attendance-system/backend/api";
   ```
   - Select ESP32 board and upload

## 🖥️ User Guide

### Admin Dashboard
- **Student Management**: Register students, assign RFID cards, enroll fingerprints
- **System Configuration**: Set attendance thresholds, manage courses
- **Reports & Analytics**: View attendance statistics, generate reports
- **User Management**: Create and manage system users

### Department Dashboard  
- **Class Mode**: Configure daily attendance tracking
- **Exam Mode**: Set up exams, generate eligibility lists
- **Student Registration**: Bulk student registration and enrollment
- **Real-time Monitoring**: Live attendance tracking

### Accountant Dashboard
- **Payment Management**: Update student payment records
- **Eligibility Control**: Override payment-based eligibility
- **Financial Reports**: Generate payment compliance reports

### Student Workflow

**Daily Attendance:**
1. Tap RFID card at class entry → System records entry time
2. Attend full class session  
3. Place finger on scanner at class exit → System records complete attendance

**Exam Attendance:**
1. System verifies eligibility (80% attendance + payment status)
2. Place finger on scanner at exam entrance → Entry recorded if eligible
3. Complete examination
4. Place finger on scanner at exam exit → Exit recorded, confirmation sent

## 🔧 API Documentation

### Authentication Endpoints

**Login**
```http
POST /api/auth.php?action=login
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "admin123"
}
```

**Check Authentication**
```http
GET /api/auth.php?action=check
```

### Student Management

**Create Student**
```http
POST /api/students.php?action=create
Content-Type: application/json

{
    "student_id": "INES2024001",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "program": "Computer Science",
    "faculty": "ICT",
    "year_of_study": 2,
    "enrollment_date": "2024-01-15"
}
```

**Assign RFID**
```http
POST /api/students.php?action=assign_rfid
Content-Type: application/json

{
    "student_id": 1,
    "rfid_code": "A1B2C3D4"
}
```

### Attendance Tracking

**Record Entry (RFID)**
```http
POST /api/attendance.php?action=record_entry
Content-Type: application/json

{
    "rfid_code": "A1B2C3D4",
    "device_id": "DEVICE_001"
}
```

**Record Exit (Fingerprint)**
```http
POST /api/attendance.php?action=record_exit
Content-Type: application/json

{
    "fingerprint_data": "fingerprint_template_hash",
    "device_id": "DEVICE_001"
}
```

### Exam Management

**Activate Exam Mode**
```http
POST /api/exams.php?action=activate
Content-Type: application/json

{
    "exam_id": 1
}
```

**Record Exam Entry**
```http
POST /api/exams.php?action=record_entry
Content-Type: application/json

{
    "fingerprint_data": "fingerprint_template_hash",
    "exam_id": 1,
    "device_id": "DEVICE_001"
}
```

## 🔒 Security Features

### Data Protection
- **Encrypted Biometric Storage**: Fingerprint templates encrypted using AES-256
- **Secure Communication**: HTTPS/TLS for all data transmission  
- **Password Hashing**: bcrypt hashing for user passwords
- **SQL Injection Prevention**: Prepared statements for all database queries

### Access Control
- **Role-Based Permissions**: Admin, Department, Accountant access levels
- **Session Management**: Secure session handling with timeout
- **Audit Logging**: Complete activity logs for all system actions
- **Device Authentication**: Hardware device verification and monitoring

### Privacy Compliance
- **Data Minimization**: Only necessary data collected and stored
- **Access Logging**: All data access attempts logged
- **Retention Policies**: Automated cleanup of old records
- **Consent Management**: Student consent for biometric data collection

## 📊 System Configuration

### Environment Variables (.env)
```bash
# Database
DB_HOST=localhost
DB_NAME=attendance_system
DB_USER=attendance_user
DB_PASS=secure_password

# Application
APP_NAME="INES Attendance System"
APP_URL=http://attendance-system.local
APP_ENV=production

# Email Settings
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# System Settings
ATTENDANCE_THRESHOLD=80
SESSION_TIMEOUT=3600
TIMEZONE=Africa/Kigali
```

### Database Configuration
- **Connection Pool**: Optimized for concurrent access
- **Indexing**: Strategic indexes for performance
- **Backup Strategy**: Daily automated backups
- **Replication**: Master-slave setup for high availability

## 🛠️ Maintenance

### Daily Tasks
```bash
# Check system status
./maintenance.sh

# View logs
tail -f logs/system.log
tail -f logs/error.log

# Monitor database
mysql -u attendance_user -p attendance_system -e "SHOW PROCESSLIST;"
```

### Weekly Tasks
```bash
# Backup system
./backup.sh

# Update statistics
php backend/scripts/update_statistics.php

# Clean old files
find uploads/ -mtime +30 -delete
```

### Database Maintenance
```sql
-- Optimize tables
OPTIMIZE TABLE students, attendance_records, exam_attendance;

-- Check table health
CHECK TABLE students, attendance_records, exam_attendance;

-- Update statistics
ANALYZE TABLE students, attendance_records, exam_attendance;
```

## 🔍 Troubleshooting

### Common Issues

**Database Connection Failed**
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection
mysql -u attendance_user -p attendance_system

# Check credentials in config file
```

**RFID Not Reading**
- Check wiring connections
- Verify power supply (3.3V)
- Test with known good RFID card
- Check serial output for error messages

**Fingerprint Scanner Issues**
- Ensure proper power (5V)
- Check UART connections (RX/TX)
- Clean scanner surface
- Re-enroll fingerprints if necessary

**WiFi Connection Problems**
- Verify network credentials
- Check signal strength
- Restart ESP32 device
- Monitor serial output for connection status

### Log Analysis
```bash
# System errors
grep "ERROR" logs/system.log

# Failed authentications
grep "authentication failed" logs/security.log

# Database issues
grep "database" logs/error.log

# Hardware problems
grep "device" logs/hardware.log
```

## 🚀 Performance Optimization

### Database Optimization
- **Indexing Strategy**: Indexes on frequently queried columns
- **Query Optimization**: Efficient joins and minimal data transfer
- **Connection Pooling**: Reuse database connections
- **Caching**: Redis/Memcached for session data

### Hardware Performance
- **Network Optimization**: Minimize API calls, batch operations
- **Power Management**: Sleep modes when inactive
- **Error Recovery**: Automatic reconnection and retry logic
- **Firmware Updates**: OTA (Over-The-Air) update capability

## 📈 Scaling Considerations

### Horizontal Scaling
- **Load Balancing**: Multiple web servers behind load balancer
- **Database Clustering**: MySQL cluster or read replicas
- **Microservices**: Break down monolithic backend
- **CDN Integration**: Static asset delivery optimization

### Vertical Scaling
- **Server Resources**: CPU, RAM, storage upgrades
- **Database Tuning**: Configuration optimization
- **Caching Layers**: Multiple levels of caching
- **Connection Limits**: Optimized connection pooling

## 🧪 Testing

### Unit Tests
```bash
# Run PHP unit tests
./vendor/bin/phpunit tests/

# Run JavaScript tests
npm test
```

### Integration Tests
```bash
# Test API endpoints
php tests/integration/api_test.php

# Test database operations
php tests/integration/database_test.php

# Test hardware communication
python tests/hardware/device_test.py
```

### Load Testing
```bash
# Simulate concurrent users
ab -n 1000 -c 10 http://attendance-system.local/api/auth.php

# Database load testing
mysqlslap --user=attendance_user --password --host=localhost \
  --query="SELECT * FROM students WHERE is_active=1" --concurrency=50 --iterations=100
```

## 📝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Follow coding standards (PSR-12 for PHP)
4. Write tests for new functionality
5. Submit pull request with detailed description

### Code Standards
- **PHP**: PSR-12 coding standard
- **JavaScript**: ESLint configuration
- **Database**: Consistent naming conventions
- **Documentation**: Inline comments and README updates

## 📞 Support

### Documentation
- **User Manual**: `docs/user_manual.md`
- **API Documentation**: `docs/api_documentation.md`
- **Hardware Guide**: `docs/hardware_setup.md`
- **Troubleshooting**: `docs/troubleshooting.md`

### Contact Information
- **Technical Support**: <EMAIL>
- **Development Team**: <EMAIL>
- **Documentation**: <EMAIL>

### Community
- **GitHub Issues**: Report bugs and request features
- **Discussion Forum**: Community support and discussions
- **Wiki**: Additional documentation and tutorials

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **INES Ruhengeri**: For project sponsorship and requirements
- **ESP32 Community**: Hardware support and libraries
- **Open Source Contributors**: Various libraries and frameworks used

---

**Built with ❤️ for INES Ruhengeri**

*Last Updated: December 2024*