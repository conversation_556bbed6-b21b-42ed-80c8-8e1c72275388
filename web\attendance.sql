-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 06, 2025 at 11:49 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `attendance_system`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `CalculateMonthlyAttendance` (IN `student_id_param` INT, IN `month_year` DATE)   BEGIN
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'partial' THEN 1 ELSE 0 END) as partial_days,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        AVG(hours_attended) as avg_hours
    FROM attendance 
    WHERE student_id = student_id_param 
    AND YEAR(attendance_date) = YEAR(month_year) 
    AND MONTH(attendance_date) = MONTH(month_year);
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `CheckExamEligibility` (IN `student_id_param` INT)   BEGIN
    DECLARE absence_count INT DEFAULT 0;
    DECLARE payment_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE required_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE attendance_eligible BOOLEAN DEFAULT FALSE;
    DECLARE finance_eligible BOOLEAN DEFAULT FALSE;
    DECLARE overall_eligible BOOLEAN DEFAULT FALSE;
    
    -- Get absence count in last 30 days
    SELECT COALESCE(SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END), 0)
    INTO absence_count
    FROM attendance 
    WHERE student_id = student_id_param 
    AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY);
    
    -- Get payment information
    SELECT COALESCE(amount_paid, 0), COALESCE(required_amount, 100)
    INTO payment_amount, required_amount
    FROM finance 
    WHERE student_id = student_id_param;
    
    -- Check eligibility
    SET attendance_eligible = (absence_count < 4);
    SET finance_eligible = (payment_amount >= required_amount);
    SET overall_eligible = (attendance_eligible AND finance_eligible);
    
    SELECT 
        student_id_param as student_id,
        absence_count,
        attendance_eligible,
        payment_amount,
        required_amount,
        finance_eligible,
        overall_eligible;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `attendance_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `attendance_date` date NOT NULL,
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `hours_attended` decimal(4,2) DEFAULT 0.00,
  `status` enum('present','partial','absent') DEFAULT 'absent',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `email_logs`
--

CREATE TABLE `email_logs` (
  `log_id` int(11) NOT NULL,
  `recipient_email` varchar(100) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `message_type` enum('warning','confirmation','notification') NOT NULL,
  `status` enum('sent','failed','pending') DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `email_logs`
--

INSERT INTO `email_logs` (`log_id`, `recipient_email`, `subject`, `message_type`, `status`, `sent_at`, `error_message`, `created_at`) VALUES
(1, '<EMAIL>', 'Test Email from Attendance System - 2025-06-29 16:13:12', '', 'sent', '2025-06-29 14:13:13', NULL, '2025-06-29 14:13:13'),
(2, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:28:04', NULL, '2025-06-29 15:28:03'),
(3, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:31:27', NULL, '2025-06-29 15:31:27'),
(4, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:35:50', NULL, '2025-06-29 15:35:50'),
(5, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:36:18', NULL, '2025-06-29 15:36:18'),
(6, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:36:53', NULL, '2025-06-29 15:36:52'),
(7, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 15:43:40', NULL, '2025-06-29 15:43:39'),
(8, '<EMAIL>', 'Function Test - 17:53:52', 'notification', 'sent', '2025-06-29 15:53:53', NULL, '2025-06-29 15:53:52'),
(9, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-06-29 16:08:00', NULL, '2025-06-29 16:08:00'),
(10, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-12 17:49:17', NULL, '2025-07-12 17:49:17'),
(11, '<EMAIL>', 'Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-12 17:49:17', NULL, '2025-07-12 17:49:17'),
(12, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:32:52', NULL, '2025-07-14 00:32:52'),
(13, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:32:52', NULL, '2025-07-14 00:32:52'),
(14, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:35:32', NULL, '2025-07-14 00:35:32'),
(15, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:35:32', NULL, '2025-07-14 00:35:32'),
(16, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:38:06', NULL, '2025-07-14 00:38:06'),
(17, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:38:06', NULL, '2025-07-14 00:38:06'),
(18, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:42:28', NULL, '2025-07-14 00:42:28'),
(19, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 00:42:29', NULL, '2025-07-14 00:42:28'),
(20, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:01:32', NULL, '2025-07-14 01:01:29'),
(21, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:01:36', NULL, '2025-07-14 01:01:32'),
(22, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:04:22', NULL, '2025-07-14 01:04:15'),
(23, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:04:26', NULL, '2025-07-14 01:04:22'),
(24, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:10:57', NULL, '2025-07-14 01:10:52'),
(25, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:12:17', NULL, '2025-07-14 01:12:13'),
(26, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:13:59', NULL, '2025-07-14 01:13:54'),
(27, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-14 01:15:10', NULL, '2025-07-14 01:15:06'),
(28, '<EMAIL>', '⚠️ Exam Eligibility Warning - Default Course', 'warning', 'sent', '2025-07-15 10:32:42', NULL, '2025-07-15 10:32:35');

-- --------------------------------------------------------

--
-- Stand-in structure for view `exam_eligibility`
-- (See below for the actual view)
--
CREATE TABLE `exam_eligibility` (
`student_id` int(11)
,`name` varchar(100)
,`email` varchar(100)
,`course` varchar(50)
,`attendance_eligible` int(1)
,`absent_count` decimal(22,0)
,`finance_eligible` int(1)
,`amount_paid` decimal(10,2)
,`required_amount` decimal(10,2)
,`overall_eligible` int(1)
);

-- --------------------------------------------------------

--
-- Table structure for table `exam_records`
--

CREATE TABLE `exam_records` (
  `exam_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `course_name` varchar(100) NOT NULL,
  `exam_date` date NOT NULL,
  `exam_time` time DEFAULT NULL,
  `attendance_eligible` tinyint(1) DEFAULT 0,
  `finance_eligible` tinyint(1) DEFAULT 0,
  `overall_eligible` tinyint(1) DEFAULT 0,
  `exam_status` enum('present','absent') DEFAULT 'absent',
  `access_time` timestamp NULL DEFAULT NULL,
  `denial_reason` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `finance`
--

CREATE TABLE `finance` (
  `finance_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `required_amount` decimal(10,2) DEFAULT 100.00,
  `amount_paid` decimal(10,2) DEFAULT 0.00,
  `last_payment_date` date DEFAULT NULL,
  `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `students`
--

CREATE TABLE `students` (
  `student_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `course` varchar(50) NOT NULL,
  `rfid_id` varchar(20) NOT NULL,
  `fingerprint_template` longtext DEFAULT NULL,
  `registration_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `student_summary`
-- (See below for the actual view)
--
CREATE TABLE `student_summary` (
`student_id` int(11)
,`name` varchar(100)
,`email` varchar(100)
,`course` varchar(50)
,`rfid_id` varchar(20)
,`status` enum('active','inactive')
,`required_amount` decimal(10,2)
,`amount_paid` decimal(10,2)
,`payment_status` enum('paid','unpaid','partial')
,`total_attendance` bigint(21)
,`present_days` decimal(22,0)
,`absent_days` decimal(22,0)
);

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `log_id` int(11) NOT NULL,
  `event_type` enum('rfid_scan','fingerprint_capture','mode_change','api_call','error','heartbeat') NOT NULL,
  `student_id` int(11) DEFAULT NULL,
  `event_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`event_data`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `setting_id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `todays_attendance`
-- (See below for the actual view)
--
CREATE TABLE `todays_attendance` (
`student_id` int(11)
,`name` varchar(100)
,`email` varchar(100)
,`course` varchar(50)
,`check_in` varchar(14)
,`check_out` varchar(15)
,`hours` decimal(4,2)
,`status` varchar(7)
);

-- --------------------------------------------------------

--
-- Structure for view `exam_eligibility`
--
DROP TABLE IF EXISTS `exam_eligibility`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `exam_eligibility`  AS SELECT `s`.`student_id` AS `student_id`, `s`.`name` AS `name`, `s`.`email` AS `email`, `s`.`course` AS `course`, CASE WHEN coalesce(`att`.`absent_count`,0) < 4 THEN 1 ELSE 0 END AS `attendance_eligible`, coalesce(`att`.`absent_count`,0) AS `absent_count`, CASE WHEN `f`.`amount_paid` >= `f`.`required_amount` THEN 1 ELSE 0 END AS `finance_eligible`, `f`.`amount_paid` AS `amount_paid`, `f`.`required_amount` AS `required_amount`, CASE WHEN coalesce(`att`.`absent_count`,0) < 4 AND `f`.`amount_paid` >= `f`.`required_amount` THEN 1 ELSE 0 END AS `overall_eligible` FROM ((`students` `s` left join `finance` `f` on(`s`.`student_id` = `f`.`student_id`)) left join (select `attendance`.`student_id` AS `student_id`,sum(case when `attendance`.`status` = 'absent' then 1 else 0 end) AS `absent_count` from `attendance` where `attendance`.`attendance_date` >= curdate() - interval 30 day group by `attendance`.`student_id`) `att` on(`s`.`student_id` = `att`.`student_id`)) WHERE `s`.`status` = 'active' ;

-- --------------------------------------------------------

--
-- Structure for view `student_summary`
--
DROP TABLE IF EXISTS `student_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `student_summary`  AS SELECT `s`.`student_id` AS `student_id`, `s`.`name` AS `name`, `s`.`email` AS `email`, `s`.`course` AS `course`, `s`.`rfid_id` AS `rfid_id`, `s`.`status` AS `status`, `f`.`required_amount` AS `required_amount`, `f`.`amount_paid` AS `amount_paid`, `f`.`payment_status` AS `payment_status`, coalesce(`att`.`attendance_count`,0) AS `total_attendance`, coalesce(`att`.`present_count`,0) AS `present_days`, coalesce(`att`.`absent_count`,0) AS `absent_days` FROM ((`students` `s` left join `finance` `f` on(`s`.`student_id` = `f`.`student_id`)) left join (select `attendance`.`student_id` AS `student_id`,count(0) AS `attendance_count`,sum(case when `attendance`.`status` = 'present' then 1 else 0 end) AS `present_count`,sum(case when `attendance`.`status` = 'absent' then 1 else 0 end) AS `absent_count` from `attendance` where `attendance`.`attendance_date` >= curdate() - interval 30 day group by `attendance`.`student_id`) `att` on(`s`.`student_id` = `att`.`student_id`)) ;

-- --------------------------------------------------------

--
-- Structure for view `todays_attendance`
--
DROP TABLE IF EXISTS `todays_attendance`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `todays_attendance`  AS SELECT `s`.`student_id` AS `student_id`, `s`.`name` AS `name`, `s`.`email` AS `email`, `s`.`course` AS `course`, coalesce(`a`.`check_in_time`,'Not Checked In') AS `check_in`, coalesce(`a`.`check_out_time`,'Not Checked Out') AS `check_out`, coalesce(`a`.`hours_attended`,0) AS `hours`, coalesce(`a`.`status`,'absent') AS `status` FROM (`students` `s` left join `attendance` `a` on(`s`.`student_id` = `a`.`student_id` and `a`.`attendance_date` = curdate())) WHERE `s`.`status` = 'active' ORDER BY `s`.`name` ASC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`attendance_id`),
  ADD UNIQUE KEY `unique_student_date` (`student_id`,`attendance_date`),
  ADD KEY `idx_date` (`attendance_date`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_student_date` (`student_id`,`attendance_date`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_recipient` (`recipient_email`),
  ADD KEY `idx_type` (`message_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sent_date` (`sent_at`);

--
-- Indexes for table `exam_records`
--
ALTER TABLE `exam_records`
  ADD PRIMARY KEY (`exam_id`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_exam_date` (`exam_date`),
  ADD KEY `idx_course` (`course_name`),
  ADD KEY `idx_eligible` (`overall_eligible`),
  ADD KEY `idx_exam_status` (`exam_status`);

--
-- Indexes for table `finance`
--
ALTER TABLE `finance`
  ADD PRIMARY KEY (`finance_id`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_payment_status` (`payment_status`);

--
-- Indexes for table `students`
--
ALTER TABLE `students`
  ADD PRIMARY KEY (`student_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `rfid_id` (`rfid_id`),
  ADD KEY `idx_rfid` (`rfid_id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `idx_event_type` (`event_type`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_created_date` (`created_at`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`setting_id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_key` (`setting_key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `attendance_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=130;

--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `exam_records`
--
ALTER TABLE `exam_records`
  MODIFY `exam_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `finance`
--
ALTER TABLE `finance`
  MODIFY `finance_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `students`
--
ALTER TABLE `students`
  MODIFY `student_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1630;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `setting_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4090;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE;

--
-- Constraints for table `exam_records`
--
ALTER TABLE `exam_records`
  ADD CONSTRAINT `exam_records_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE;

--
-- Constraints for table `finance`
--
ALTER TABLE `finance`
  ADD CONSTRAINT `finance_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE CASCADE;

--
-- Constraints for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`student_id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
