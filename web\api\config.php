<?php
/**
 * Database Configuration File
 * Attendance System API
 * Fixed Version - No Parse Errors
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// ==========================================
// EMAIL MODE CONFIGURATION
// ==========================================
// Set to 'simulation' for testing or 'phpmailer' for real emails
 // Change to 'phpmailer' for real emails
define('EMAIL_MODE', 'phpmailer');
// Database Configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "attendance_system";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// ==========================================
// EMAIL CONFIGURATION
// ==========================================

// Email Provider Configurations
$email_providers = [
    'gmail' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'smtp_username' => '<EMAIL>',        // ← UPDATE THIS
        'smtp_password' => 'htkb dphz dgxn whas',           // ← UPDATE THIS (App Password)
    ],
    'outlook' => [
        'smtp_host' => 'smtp-mail.outlook.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'smtp_username' => '<EMAIL>',      // ← UPDATE THIS
        'smtp_password' => 'your-password',               // ← UPDATE THIS
    ],
    'custom' => [
        'smtp_host' => '<EMAIL>',           // ← UPDATE THIS
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'smtp_username' => '<EMAIL>', // ← UPDATE THIS
        'smtp_password' => 'htkb dphz dgxn whas',               // ← UPDATE THIS
    ]
];

// Active email configuration
$active_provider = 'gmail'; // Change to 'outlook' or 'custom' as needed
$email_config = array_merge($email_providers[$active_provider], [
    'from_email' => '<EMAIL>',         // ← UPDATE THIS
    'from_name' => 'Attendance Management System',       // ← UPDATE THIS
    'smtp_debug' => 0,  // Set to 2 for debugging, 0 for production
    'enable_html' => true,  // Set to false for plain text emails
]);

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

function logSystemEvent($pdo, $event_type, $student_id = null, $event_data = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event_type, student_id, event_data, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_type,
            $student_id,
            json_encode($event_data),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch(Exception $e) {
        // Log error silently, don't break main functionality
        error_log("System logging failed: " . $e->getMessage());
    }
}

function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit();
}

function validateRequired($data, $required_fields) {
    $missing = [];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    return $missing;
}

function getSetting($pdo, $key, $default = null) {
    try {
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
    } catch(Exception $e) {
        return $default;
    }
}

function updateSetting($pdo, $key, $value) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$key, $value, $value]);
        return true;
    } catch(Exception $e) {
        return false;
    }
}

// ==========================================
// EMAIL FUNCTIONS
// ==========================================

/**
 * Main email sending function - supports both simulation and real email
 */
function sendEmail($to, $subject, $message, $type = 'notification') {
    global $pdo;
    
    // Choose email method based on configuration
    if (EMAIL_MODE === 'phpmailer') {
        return sendEmailPHPMailer($to, $subject, $message, $type);
    } else {
        return sendEmailSimulation($to, $subject, $message, $type);
    }
}

/**
 * PHPMailer implementation for real email sending
 */
function sendEmailPHPMailer($to, $subject, $message, $type = 'notification') {
    global $pdo, $email_config;
    
    // Check if PHPMailer files exist
    $phpmailer_path = __DIR__ . '/vendor/phpmailer/phpmailer/src/';
    if (!file_exists($phpmailer_path . 'PHPMailer.php')) {
        error_log("PHPMailer files not found, falling back to simulation");
        return sendEmailSimulation($to, $subject, $message, $type);
    }
    
    // Load PHPMailer files
    require_once $phpmailer_path . 'PHPMailer.php';
    require_once $phpmailer_path . 'SMTP.php';
    require_once $phpmailer_path . 'Exception.php';
    
    // Log email attempt first
    $log_id = logEmailAttempt($pdo, $to, $subject, $type);
    
    try {
        // Create PHPMailer instance (using full class names to avoid use statements)
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $email_config['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $email_config['smtp_username'];
        $mail->Password = $email_config['smtp_password'];
        
        // Set encryption type
        if ($email_config['smtp_secure'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } else {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        }
        
        $mail->Port = $email_config['smtp_port'];
        
        // Debug settings (enable for troubleshooting)
        $mail->SMTPDebug = $email_config['smtp_debug'];
        
        // Optional: Add SSL options for compatibility
        if ($email_config['smtp_debug'] == 0) {
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );
        }
        
        // Recipients
        $mail->setFrom($email_config['from_email'], $email_config['from_name']);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML($email_config['enable_html']);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        // If HTML is enabled but message is plain text, also set AltBody
        if ($email_config['enable_html']) {
            $mail->AltBody = strip_tags($message);
        }
        
        // Send email
        $mail->send();
        
        // Update log as sent
        updateEmailLog($pdo, $log_id, 'sent');
        
        return true;
        
    } catch (Exception $e) {
        // Update log as failed
        updateEmailLog($pdo, $log_id, 'failed', $e->getMessage());
        
        // Log error for debugging
        error_log("PHPMailer Error: " . $e->getMessage());
        
        return false;
    }
}

/**
 * Simulation implementation for development/testing
 */
function sendEmailSimulation($to, $subject, $message, $type = 'notification') {
    global $pdo;
    
    try {
        // Log email attempt
        $log_id = logEmailAttempt($pdo, $to, $subject, $type);
        
        // Simulate email sending (always successful in simulation)
        $success = true;
        
        // Add small delay to simulate real email sending
        usleep(100000); // 0.1 second delay
        
        if ($success) {
            updateEmailLog($pdo, $log_id, 'sent');
            
            // Log simulation notice
            error_log("EMAIL SIMULATION: Email would be sent to $to with subject: $subject");
            
            return true;
        } else {
            updateEmailLog($pdo, $log_id, 'failed', 'Simulation failed');
            return false;
        }
    } catch(Exception $e) {
        error_log("Email simulation error: " . $e->getMessage());
        return false;
    }
}

/**
 * Helper function to log email attempts
 */
function logEmailAttempt($pdo, $to, $subject, $type) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO email_logs (recipient_email, subject, message_type, status) 
            VALUES (?, ?, ?, 'pending')
        ");
        $stmt->execute([$to, $subject, $type]);
        return $pdo->lastInsertId();
    } catch(Exception $e) {
        error_log("Email logging failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Helper function to update email log status
 */
function updateEmailLog($pdo, $log_id, $status, $error_message = null) {
    if (!$log_id) return;
    
    try {
        if ($status === 'sent') {
            $stmt = $pdo->prepare("
                UPDATE email_logs 
                SET status = 'sent', sent_at = CURRENT_TIMESTAMP 
                WHERE log_id = ?
            ");
            $stmt->execute([$log_id]);
        } else {
            $stmt = $pdo->prepare("
                UPDATE email_logs 
                SET status = 'failed', error_message = ? 
                WHERE log_id = ?
            ");
            $stmt->execute([$error_message, $log_id]);
        }
    } catch(Exception $e) {
        error_log("Email log update failed: " . $e->getMessage());
    }
}

/**
 * Get current email system status
 */
function getEmailSystemStatus() {
    global $email_config;
    
    $status = [
        'mode' => EMAIL_MODE,
        'provider' => 'Unknown',
        'configured' => false,
        'last_test' => 'Never'
    ];
    
    // Determine provider
    if (strpos($email_config['smtp_host'], 'gmail') !== false) {
        $status['provider'] = 'Gmail';
    } elseif (strpos($email_config['smtp_host'], 'outlook') !== false) {
        $status['provider'] = 'Outlook';
    } else {
        $status['provider'] = 'Custom SMTP';
    }
    
    // Check if configured
    if (EMAIL_MODE === 'phpmailer') {
        $status['configured'] = !empty($email_config['smtp_username']) && !empty($email_config['smtp_password']);
    } else {
        $status['configured'] = true; // Simulation always configured
    }
    
    return $status;
}

// ==========================================
// EMAIL TEMPLATE FUNCTIONS
// ==========================================

/**
 * Generate HTML warning email template
 */
function generateWarningEmailHTML($student_name, $course, $exam_date, $reasons) {
    $reasons_html = '';
    foreach ($reasons as $reason) {
        $reasons_html .= "<li style='color: #e74c3c; margin: 5px 0;'>$reason</li>";
    }
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 0; }
            .header { background: #e74c3c; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .footer { background: #34495e; color: white; padding: 15px; text-align: center; font-size: 12px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .contact { background: #e8f4fd; border: 1px solid #bee5eb; padding: 15px; margin: 15px 0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2 style='margin: 0;'>⚠️ Exam Eligibility Warning</h2>
            </div>
            <div class='content'>
                <p>Dear <strong>$student_name</strong>,</p>
                
                <div class='warning'>
                    <h3 style='margin-top: 0; color: #856404;'>You are currently not eligible to attend the upcoming exam:</h3>
                    <p style='margin-bottom: 0;'><strong>Course:</strong> $course<br>
                       <strong>Date:</strong> $exam_date</p>
                </div>
                
                <p><strong>Reason(s) for ineligibility:</strong></p>
                <ul style='padding-left: 20px;'>$reasons_html</ul>
                
                <div class='contact'>
                    <p style='margin: 0;'><strong>Action Required:</strong> Please contact the administration office immediately to resolve these issues before the exam date.</p>
                </div>
                
                <p style='margin-bottom: 0;'>📞 <strong>Contact Information:</strong><br>
                   Email: <EMAIL><br>
                   Office Hours: 9:00 AM - 5:00 PM, Monday - Friday</p>
            </div>
            <div class='footer'>
                <p style='margin: 0;'>Attendance Management System<br>
                University Administration</p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Generate HTML confirmation email template
 */
function generateConfirmationEmailHTML($student_name, $course, $date, $time) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 0; }
            .header { background: #27ae60; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .footer { background: #34495e; color: white; padding: 15px; text-align: center; font-size: 12px; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .info { background: #e8f4fd; border: 1px solid #bee5eb; padding: 15px; margin: 15px 0; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2 style='margin: 0;'>✅ Exam Attendance Confirmation</h2>
            </div>
            <div class='content'>
                <p>Dear <strong>$student_name</strong>,</p>
                
                <div class='success'>
                    <h3 style='margin-top: 0; color: #155724;'>Your exam attendance has been confirmed!</h3>
                    <p style='margin-bottom: 0;'><strong>Course:</strong> $course<br>
                       <strong>Date:</strong> $date<br>
                       <strong>Time:</strong> $time</p>
                </div>
                
                <div class='info'>
                    <p style='margin: 0;'>This email serves as official confirmation that you attended the exam. Please keep this email for your records.</p>
                </div>
                
                <p style='margin-bottom: 0;'>If you have any questions about your exam results or need additional documentation, please contact the administration office.</p>
            </div>
            <div class='footer'>
                <p style='margin: 0;'>Attendance Management System<br>
                University Administration</p>
            </div>
        </div>
    </body>
    </html>";
}

// Common response messages
$messages = [
    'success' => 'Operation completed successfully',
    'error' => 'An error occurred while processing your request',
    'not_found' => 'Record not found',
    'invalid_data' => 'Invalid data provided',
    'access_denied' => 'Access denied',
    'student_not_found' => 'Student not found',
    'already_exists' => 'Record already exists'
];
?>