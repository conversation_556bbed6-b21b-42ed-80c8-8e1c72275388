<?php
/**
 * COMPLETE FIXED System API - Handles heartbeat and system monitoring
 * File: web/api/system.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// Include database configuration
require_once 'config.php';

// Get request data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

// Response array
$response = [
    'success' => false,
    'message' => '',
    'data' => null,
    'timestamp' => date('Y-m-d H:i:s')
];

try {
    // Create database connection
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    switch ($action) {
        case 'heartbeat':
            handleHeartbeat($pdo, $input);
            break;
        
        case 'system_status':
            getSystemStatus($pdo);
            break;
        
        case 'logs':
            getSystemLogs($pdo);
            break;
        
        case 'settings':
            getSystemSettings($pdo);
            break;
        
        case 'update_setting':
            updateSystemSetting($pdo, $input);
            break;
        
        case 'test':
            testEndpoint($pdo);
            break;
        
        default:
            $response['success'] = true;
            $response['message'] = 'System API is active';
            $response['data'] = ['status' => 'ok', 'time' => date('Y-m-d H:i:s')];
    }

} catch (PDOException $e) {
    $response['message'] = 'Database error: ' . $e->getMessage();
    error_log('Database error in system.php: ' . $e->getMessage());
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log('Error in system.php: ' . $e->getMessage());
}

echo json_encode($response);

/**
 * Handle ESP32 heartbeat
 */
function handleHeartbeat($pdo, $data) {
    global $response;
    
    try {
        // Extract heartbeat data
        $mode = $data['mode'] ?? 'UNKNOWN';
        $uptime = $data['uptime'] ?? 0;
        $wifi_signal = $data['wifi_signal'] ?? 0;
        $free_heap = $data['free_heap'] ?? 0;
        $retry_count = $data['retry_count'] ?? 0;
        $rfid_status = $data['rfid_status'] ?? 0;
        $fingerprint_status = $data['fingerprint_status'] ?? 0;
        $fingerprint_templates = $data['fingerprint_templates'] ?? 0;
        $fingerprint_capacity = $data['fingerprint_capacity'] ?? 0;
        $system_ready = $data['system_ready'] ?? false;
        
        // Log the heartbeat
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event_type, event_data, ip_address, created_at) 
            VALUES ('heartbeat', ?, ?, NOW())
        ");
        
        $heartbeat_data = json_encode([
            'mode' => $mode,
            'uptime' => $uptime,
            'wifi_signal' => $wifi_signal,
            'free_heap' => $free_heap,
            'retry_count' => $retry_count,
            'rfid_status' => $rfid_status,
            'fingerprint_status' => $fingerprint_status,
            'fingerprint_templates' => $fingerprint_templates,
            'fingerprint_capacity' => $fingerprint_capacity,
            'system_ready' => $system_ready,
            'description' => 'ESP32 heartbeat received'
        ]);
        
        $stmt->execute([$heartbeat_data, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
        
        // Update system settings with last heartbeat AND current mode
        $updateStmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value, description) 
            VALUES ('last_heartbeat', ?, 'Last ESP32 heartbeat timestamp')
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            updated_at = NOW()
        ");
        $updateStmt->execute([date('Y-m-d H:i:s')]);
        
        // Store current mode
        $modeStmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value, description) 
            VALUES ('current_mode', ?, 'Current ESP32 operating mode')
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            updated_at = NOW()
        ");
        $modeStmt->execute([$mode]);
        
        $response['success'] = true;
        $response['message'] = 'Heartbeat received successfully';
        $response['data'] = [
            'server_time' => date('Y-m-d H:i:s'),
            'status' => 'ok',
            'mode_received' => $mode
        ];
        
    } catch (Exception $e) {
        error_log("Heartbeat processing error: " . $e->getMessage());
        throw new Exception('Failed to process heartbeat: ' . $e->getMessage());
    }
}

/**
 * Get system status overview
 */
function getSystemStatus($pdo) {
    global $response;
    
    try {
        // Get last heartbeat
        $heartbeatStmt = $pdo->prepare("
            SELECT setting_value FROM system_settings 
            WHERE setting_key = 'last_heartbeat'
        ");
        $heartbeatStmt->execute();
        $lastHeartbeat = $heartbeatStmt->fetchColumn();
        
        // Get current mode
        $modeStmt = $pdo->prepare("
            SELECT setting_value FROM system_settings 
            WHERE setting_key = 'current_mode'
        ");
        $modeStmt->execute();
        $currentMode = $modeStmt->fetchColumn() ?: 'UNKNOWN';
        
        // Get student count
        $studentStmt = $pdo->prepare("SELECT COUNT(*) FROM students WHERE status = 'active'");
        $studentStmt->execute();
        $studentCount = $studentStmt->fetchColumn();
        
        // Get today's attendance
        $attendanceStmt = $pdo->prepare("
            SELECT COUNT(*) FROM attendance 
            WHERE attendance_date = CURDATE()
        ");
        $attendanceStmt->execute();
        $todayAttendance = $attendanceStmt->fetchColumn();
        
        // Calculate system uptime
        $systemOnline = false;
        $timeSinceHeartbeat = null;
        if ($lastHeartbeat) {
            $heartbeatTime = strtotime($lastHeartbeat);
            $timeSinceHeartbeat = time() - $heartbeatTime;
            $systemOnline = $timeSinceHeartbeat < 120; // Online if heartbeat within 2 minutes
        }
        
        $response['success'] = true;
        $response['data'] = [
            'system_online' => $systemOnline,
            'last_heartbeat' => $lastHeartbeat,
            'current_mode' => $currentMode,
            'time_since_heartbeat' => $timeSinceHeartbeat,
            'total_students' => (int)$studentCount,
            'todays_attendance' => (int)$todayAttendance,
            'server_time' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        throw new Exception('Failed to get system status: ' . $e->getMessage());
    }
}

/**
 * FIXED: Get recent system logs - Using sprintf instead of prepared statement for LIMIT/OFFSET
 */
function getSystemLogs($pdo) {
    global $response;
    
    try {
        // FIXED: Cast to integers and validate
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        
        // Validate limits to prevent abuse
        $limit = min(max($limit, 1), 100); // Between 1 and 100
        $offset = max($offset, 0); // At least 0
        
        // FIXED: Use sprintf to build query with validated integers
        $sql = sprintf("
            SELECT 
                log_id,
                event_type,
                student_id,
                event_data,
                ip_address,
                created_at
            FROM system_logs 
            ORDER BY created_at DESC 
            LIMIT %d OFFSET %d
        ", $limit, $offset);
        
        $stmt = $pdo->query($sql);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Parse JSON event data
        foreach ($logs as &$log) {
            if ($log['event_data']) {
                $log['event_data'] = json_decode($log['event_data'], true);
            }
        }
        
        $response['success'] = true;
        $response['data'] = $logs;
        $response['count'] = count($logs);
        $response['limit'] = $limit;
        $response['offset'] = $offset;
        
    } catch (Exception $e) {
        throw new Exception('Failed to get system logs: ' . $e->getMessage());
    }
}

/**
 * Get system settings
 */
function getSystemSettings($pdo) {
    global $response;
    
    try {
        $stmt = $pdo->prepare("
            SELECT setting_key, setting_value, description, updated_at
            FROM system_settings 
            ORDER BY setting_key
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['success'] = true;
        $response['data'] = $settings;
        
    } catch (Exception $e) {
        throw new Exception('Failed to get system settings: ' . $e->getMessage());
    }
}

/**
 * Update system setting
 */
function updateSystemSetting($pdo, $data) {
    global $response;
    
    try {
        $key = $data['key'] ?? '';
        $value = $data['value'] ?? '';
        $description = $data['description'] ?? '';
        
        if (!$key) {
            throw new Exception('Setting key is required');
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value, description) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            description = VALUES(description),
            updated_at = NOW()
        ");
        $stmt->execute([$key, $value, $description]);
        
        $response['success'] = true;
        $response['message'] = 'Setting updated successfully';
        
    } catch (Exception $e) {
        throw new Exception('Failed to update setting: ' . $e->getMessage());
    }
}

/**
 * Test endpoint
 */
function testEndpoint($pdo) {
    global $response;
    
    $response['success'] = true;
    $response['message'] = 'API is working correctly';
    $response['data'] = [
        'server_time' => date('Y-m-d H:i:s'),
        'php_version' => phpversion(),
        'database_connected' => true,
        'test_mode' => 'System API test successful'
    ];
}
?>