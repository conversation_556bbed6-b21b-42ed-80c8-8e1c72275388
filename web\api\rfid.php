<?php
/**
 * RFID API - Handle RFID Detection and Processing
 * This file was missing and causing 404 errors
 */

require_once 'config.php';

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'rfid_detected':
        handleRFIDDetected($pdo, $input);
        break;
    case 'verify_rfid':
        verifyRFID($pdo, $input);
        break;
    default:
        sendResponse(['success' => false, 'message' => 'Invalid action'], 400);
}

/**
 * Handle RFID card detection from ESP32
 */
function handleRFIDDetected($pdo, $data) {
    try {
        $rfid_id = $data['rfid_id'] ?? '';
        $mode = $data['mode'] ?? 'UNKNOWN';
        $timestamp = $data['timestamp'] ?? time();
        
        if (empty($rfid_id)) {
            sendResponse(['success' => false, 'message' => 'RFID ID required'], 400);
        }
        
        // Log the RFID detection
        logSystemEvent($pdo, 'rfid_scan', null, [
            'rfid_id' => $rfid_id,
            'mode' => $mode,
            'timestamp' => $timestamp,
            'esp32_time' => $timestamp
        ]);
        
        // Check if RFID exists in system
        $stmt = $pdo->prepare("
            SELECT student_id, name, email, course, status 
            FROM students 
            WHERE rfid_id = ?
        ");
        $stmt->execute([$rfid_id]);
        $student = $stmt->fetch();
        
        if ($student) {
            // Known student
            sendResponse([
                'success' => true,
                'message' => 'RFID recognized',
                'student_found' => true,
                'student' => $student,
                'action' => 'proceed_with_' . strtolower($mode)
            ]);
        } else {
            // Unknown RFID
            if ($mode === 'REGISTRATION') {
                // In registration mode, unknown RFID is expected
                sendResponse([
                    'success' => true,
                    'message' => 'New RFID detected, ready for registration',
                    'student_found' => false,
                    'rfid_id' => $rfid_id,
                    'action' => 'await_student_details'
                ]);
            } else {
                // In other modes, unknown RFID is an error
                sendResponse([
                    'success' => false,
                    'message' => 'RFID not registered in system',
                    'student_found' => false,
                    'rfid_id' => $rfid_id,
                    'action' => 'registration_required'
                ], 404);
            }
        }
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error processing RFID: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Verify RFID for attendance/exam purposes
 */
function verifyRFID($pdo, $data) {
    try {
        $rfid_id = $data['rfid_id'] ?? '';
        
        if (empty($rfid_id)) {
            sendResponse(['success' => false, 'message' => 'RFID ID required'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT s.student_id, s.name, s.email, s.course, s.status,
                   f.required_amount, f.amount_paid, f.payment_status
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            WHERE s.rfid_id = ? AND s.status = 'active'
        ");
        $stmt->execute([$rfid_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse([
                'success' => false,
                'message' => 'RFID not found or student inactive'
            ], 404);
        }
        
        sendResponse([
            'success' => true,
            'message' => 'RFID verified',
            'student' => $student
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error verifying RFID: ' . $e->getMessage()
        ], 500);
    }
}
?>