<?php
/**
 * fingerprint.php - ESP32 Fingerprint Processing API
 * Handles fingerprint data from ESP32 and creates system logs for frontend
 * FIXED VERSION: Now properly saves fingerprint to student records
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// Log all fingerprint requests for debugging
error_log("Fingerprint API called - Method: $method, Data: " . json_encode($input));

if ($method === 'POST') {
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'fingerprint_captured':
            handleFingerprintCaptured($pdo, $input);
            break;
            
        case 'enroll':
            handleFingerprintEnroll($pdo, $input);
            break;
            
        case 'verify':
            handleFingerprintVerify($pdo, $input);
            break;
            
        default:
            // Default handler for ESP32 fingerprint data
            handleFingerprintCaptured($pdo, $input);
            break;
    }
} else if ($method === 'GET') {
    $action = $_GET['action'] ?? 'status';
    
    switch ($action) {
        case 'status':
            echo json_encode(['success' => true, 'message' => 'Fingerprint API is active']);
            break;
            
        case 'list':
            listFingerprints($pdo);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
}

/**
 * MAIN FUNCTION: Handle fingerprint captured from ESP32
 * FIXED: Now properly saves fingerprint to student database
 */
function handleFingerprintCaptured($pdo, $data) {
    try {
        $fingerprint_id = $data['fingerprint_id'] ?? $data['fingerprint_id_str'] ?? null;
        $mode = $data['mode'] ?? 'UNKNOWN';
        $device_id = $data['device_id'] ?? 'ESP32_001';
        $wifi_signal = $data['wifi_signal'] ?? null;
        $esp32_uptime = $data['esp32_uptime'] ?? null;
        
        // Validate fingerprint ID
        if (empty($fingerprint_id)) {
            throw new Exception('Fingerprint ID is required');
        }
        
        // FIXED: Save fingerprint to student record if in registration mode
        if ($mode === 'REGISTRATION') {
            error_log("🔄 FINGERPRINT SAVE: Updating student record with fingerprint ID $fingerprint_id");
            
            $stmt = $pdo->prepare("
                UPDATE students 
                SET fingerprint_template = ?, updated_at = NOW() 
                WHERE fingerprint_template IS NULL 
                AND status = 'active' 
                ORDER BY registration_date DESC 
                LIMIT 1
            ");
            $result = $stmt->execute([$fingerprint_id]);
            
            if ($result && $stmt->rowCount() > 0) {
                error_log("✅ FINGERPRINT SAVE: Successfully updated student record with fingerprint ID $fingerprint_id");
                
                // Get the updated student info for logging
                $stmt = $pdo->prepare("
                    SELECT student_id, name, rfid_id 
                    FROM students 
                    WHERE fingerprint_template = ? 
                    AND status = 'active'
                ");
                $stmt->execute([$fingerprint_id]);
                $student = $stmt->fetch();
                
                if ($student) {
                    error_log("📝 STUDENT UPDATED: ID {$student['student_id']}, Name: {$student['name']}, RFID: {$student['rfid_id']}, Fingerprint: $fingerprint_id");
                }
            } else {
                error_log("❌ FINGERPRINT SAVE: No student record found to update or update failed");
            }
        }
        
        // Create system log entry for frontend to pick up
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event_type, event_data, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $event_data = json_encode([
            'fingerprint_id' => $fingerprint_id,
            'mode' => $mode,
            'device_id' => $device_id,
            'wifi_signal' => $wifi_signal,
            'esp32_uptime' => $esp32_uptime,
            'timestamp' => time(),
            'description' => "Fingerprint ID $fingerprint_id captured in $mode mode"
        ]);
        
        $stmt->execute([
            'fingerprint_capture',
            $event_data,
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            $_SERVER['HTTP_USER_AGENT'] ?? 'ESP32'
        ]);
        
        $log_id = $pdo->lastInsertId();
        
        // Also log the heartbeat data if present
        if ($esp32_uptime !== null) {
            logHeartbeat($pdo, $data);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Fingerprint captured and logged for ' . strtolower($mode),
            'fingerprint_id' => $fingerprint_id,
            'mode' => $mode,
            'action' => strtolower($mode) . '_fingerprint_ready',
            'next_step' => $mode === 'REGISTRATION' ? 'complete_student_registration' : 'process_attendance',
            'log_id' => $log_id,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        error_log("Fingerprint capture error: " . $e->getMessage());
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Error processing fingerprint: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle fingerprint enrollment
 */
function handleFingerprintEnroll($pdo, $data) {
    try {
        $student_id = $data['student_id'] ?? null;
        $fingerprint_data = $data['fingerprint_data'] ?? null;
        
        if (!$student_id || !$fingerprint_data) {
            throw new Exception('Student ID and fingerprint data are required');
        }
        
        // Update student with fingerprint template
        $stmt = $pdo->prepare("
            UPDATE students 
            SET fingerprint_template = ?, updated_at = NOW() 
            WHERE student_id = ?
        ");
        
        $stmt->execute([$fingerprint_data, $student_id]);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('Student not found or fingerprint not updated');
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Fingerprint enrolled successfully',
            'student_id' => $student_id
        ]);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Enrollment error: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle fingerprint verification
 */
function handleFingerprintVerify($pdo, $data) {
    try {
        $fingerprint_id = $data['fingerprint_id'] ?? null;
        
        if (!$fingerprint_id) {
            throw new Exception('Fingerprint ID is required for verification');
        }
        
        // Look up student by fingerprint template
        $stmt = $pdo->prepare("
            SELECT student_id, name, email, rfid_id, status 
            FROM students 
            WHERE fingerprint_template = ? AND status = 'active'
        ");
        
        $stmt->execute([$fingerprint_id]);
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student) {
            echo json_encode([
                'success' => true,
                'message' => 'Fingerprint verified successfully',
                'student' => $student,
                'verified' => true
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Fingerprint not recognized or student inactive',
                'verified' => false
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Verification error: ' . $e->getMessage()
        ]);
    }
}

/**
 * List all enrolled fingerprints
 */
function listFingerprints($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT student_id, name, email, fingerprint_template, created_at 
            FROM students 
            WHERE fingerprint_template IS NOT NULL 
            ORDER BY created_at DESC
        ");
        
        $stmt->execute();
        $fingerprints = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $fingerprints,
            'count' => count($fingerprints)
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Error retrieving fingerprints: ' . $e->getMessage()
        ]);
    }
}

/**
 * Log ESP32 heartbeat data
 */
function logHeartbeat($pdo, $data) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (event_type, event_data, ip_address, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        
        $event_data = json_encode([
            'mode' => $data['mode'] ?? 'UNKNOWN',
            'device_id' => $data['device_id'] ?? 'ESP32_001',
            'esp32_uptime' => $data['esp32_uptime'] ?? 0,
            'wifi_signal' => $data['wifi_signal'] ?? null,
            'sensor_templates' => $data['sensor_templates'] ?? 0,
            'sensor_capacity' => $data['sensor_capacity'] ?? 150,
            'sensor_status' => $data['sensor_status'] ?? 'unknown',
            'timestamp' => time(),
            'description' => 'ESP32 heartbeat received'
        ]);
        
        $stmt->execute([
            'heartbeat',
            $event_data,
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
        ]);
        
    } catch (Exception $e) {
        error_log("Heartbeat logging error: " . $e->getMessage());
    }
}
?>