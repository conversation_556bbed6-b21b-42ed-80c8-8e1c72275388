<?php
/**
 * Attendance API - Handle Daily Attendance Tracking
 * Attendance System
 */

require_once 'config.php';

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'checkin':
        recordCheckIn($pdo, $input);
        break;
    case 'checkout':
        recordCheckOut($pdo, $input);
        break;
    case 'today':
        getTodayAttendance($pdo);
        break;
    case 'date':
        getAttendanceByDate($pdo, $input['date'] ?? '');
        break;
    case 'student':
        getStudentAttendance($pdo, $input['student_id'] ?? '', $input['start_date'] ?? '', $input['end_date'] ?? '');
        break;
    case 'monthly':
        getMonthlyAttendance($pdo, $input['month'] ?? '', $input['year'] ?? '');
        break;
    case 'stats':
        getAttendanceStats($pdo);
        break;
    case 'update':
        updateAttendance($pdo, $input);
        break;
    case 'mark_absent':
        markStudentsAbsent($pdo, $input['date'] ?? '');
        break;
    case 'generate_test_data':          // ← To this
    generateTestAttendance($pdo, $input);
    break;
    case 'clear_today':
        clearTodayAttendance($pdo);
        break;
    default:
        sendResponse(['success' => false, 'message' => 'Invalid action'], 400);
}

/**
 * Record student check-in via RFID
 */
function recordCheckIn($pdo, $data) {
    try {
        $rfid_id = $data['rfid_id'] ?? '';
        $timestamp = $data['timestamp'] ?? date('Y-m-d H:i:s');
        
        if (empty($rfid_id)) {
            sendResponse(['success' => false, 'message' => 'RFID ID required'], 400);
        }
        
        // Get student by RFID
        $stmt = $pdo->prepare("
            SELECT student_id, name FROM students 
            WHERE rfid_id = ? AND status = 'active'
        ");
        $stmt->execute([$rfid_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse(['success' => false, 'message' => 'RFID not registered'], 404);
        }
        
        $student_id = $student['student_id'];
        $today = date('Y-m-d');
        $check_in_time = date('H:i:s', strtotime($timestamp));
        
        // Check if already checked in today
        $stmt = $pdo->prepare("
            SELECT attendance_id, check_in_time 
            FROM attendance 
            WHERE student_id = ? AND attendance_date = ?
        ");
        $stmt->execute([$student_id, $today]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            sendResponse([
                'success' => false,
                'message' => 'Already checked in today at ' . $existing['check_in_time'],
                'already_checked_in' => true,
                'existing_time' => $existing['check_in_time']
            ], 409);
        }
        
        // Record check-in
        $stmt = $pdo->prepare("
            INSERT INTO attendance (student_id, attendance_date, check_in_time, status) 
            VALUES (?, ?, ?, 'partial')
        ");
        $stmt->execute([$student_id, $today, $check_in_time]);
        
        logSystemEvent($pdo, 'checkin', $student_id, [
            'rfid_id' => $rfid_id,
            'time' => $check_in_time,
            'date' => $today
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Check-in recorded successfully',
            'student_name' => $student['name'],
            'check_in_time' => $check_in_time,
            'next_step' => 'fingerprint_required'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error recording check-in: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Record student check-out via fingerprint
 */
function recordCheckOut($pdo, $data) {
    try {
        $rfid_id = $data['rfid_id'] ?? '';
        $fingerprint_id = $data['fingerprint_id'] ?? '';
        $timestamp = $data['timestamp'] ?? date('Y-m-d H:i:s');
        
        if (empty($rfid_id)) {
            sendResponse(['success' => false, 'message' => 'RFID ID required'], 400);
        }
        
        // Get student by RFID
        $stmt = $pdo->prepare("
            SELECT student_id, name FROM students 
            WHERE rfid_id = ? AND status = 'active'
        ");
        $stmt->execute([$rfid_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse(['success' => false, 'message' => 'RFID not registered'], 404);
        }
        
        $student_id = $student['student_id'];
        $today = date('Y-m-d');
        $check_out_time = date('H:i:s', strtotime($timestamp));
        
        // Get today's attendance record
        $stmt = $pdo->prepare("
            SELECT attendance_id, check_in_time, check_out_time 
            FROM attendance 
            WHERE student_id = ? AND attendance_date = ?
        ");
        $stmt->execute([$student_id, $today]);
        $attendance = $stmt->fetch();
        
        if (!$attendance) {
            sendResponse([
                'success' => false,
                'message' => 'No check-in record found for today'
            ], 404);
        }
        
        if ($attendance['check_out_time']) {
            sendResponse([
                'success' => false,
                'message' => 'Already checked out today at ' . $attendance['check_out_time'],
                'already_checked_out' => true
            ], 409);
        }
        
        // Calculate hours attended
        $check_in = new DateTime($today . ' ' . $attendance['check_in_time']);
        $check_out = new DateTime($today . ' ' . $check_out_time);
        $interval = $check_in->diff($check_out);
        $hours_attended = $interval->h + ($interval->i / 60);
        
        // Determine status based on hours
        $min_hours = getSetting($pdo, 'minimum_daily_hours', 8.0);
        $partial_hours = getSetting($pdo, 'partial_attendance_hours', 4.0);
        
        if ($hours_attended >= $min_hours) {
            $status = 'present';
        } elseif ($hours_attended >= $partial_hours) {
            $status = 'partial';
        } else {
            $status = 'absent';
        }
        
        // Update attendance record
        $stmt = $pdo->prepare("
            UPDATE attendance 
            SET check_out_time = ?, hours_attended = ?, status = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE attendance_id = ?
        ");
        $stmt->execute([$check_out_time, $hours_attended, $status, $attendance['attendance_id']]);
        
        logSystemEvent($pdo, 'checkout', $student_id, [
            'rfid_id' => $rfid_id,
            'fingerprint_id' => $fingerprint_id,
            'check_out_time' => $check_out_time,
            'hours_attended' => $hours_attended,
            'status' => $status
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Check-out recorded successfully',
            'student_name' => $student['name'],
            'check_out_time' => $check_out_time,
            'hours_attended' => round($hours_attended, 2),
            'status' => $status
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error recording check-out: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get today's attendance data
 */
function getTodayAttendance($pdo) {
    try {
        $today = date('Y-m-d');
        
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name as student_name,
                s.email,
                s.course,
                COALESCE(a.check_in_time, 'Not Checked In') as check_in,
                COALESCE(a.check_out_time, 'Not Checked Out') as check_out,
                COALESCE(a.hours_attended, 0) as hours,
                COALESCE(a.status, 'absent') as status
            FROM students s
            LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = ?
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        $stmt->execute([$today]);
        $attendance = $stmt->fetchAll();
        
        // Calculate statistics
        $stats = [
            'total_students' => count($attendance),
            'present' => 0,
            'partial' => 0,
            'absent' => 0
        ];
        
        foreach ($attendance as $record) {
            $stats[$record['status']]++;
        }
        
        sendResponse([
    'success' => true,
    'date' => $today,
    'data' => $attendance,          // ← To this
    'stats' => $stats
]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching today\'s attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get attendance for specific date
 */
function getAttendanceByDate($pdo, $date) {
    try {
        if (empty($date)) {
            $date = date('Y-m-d');
        }
        
        // Validate date format
        if (!DateTime::createFromFormat('Y-m-d', $date)) {
            sendResponse(['success' => false, 'message' => 'Invalid date format'], 400);
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name as student_name,
                s.email,
                s.course,
                a.check_in_time,
                a.check_out_time,
                a.hours_attended,
                a.status,
                a.notes
            FROM students s
            LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = ?
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        $stmt->execute([$date]);
        $attendance = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'date' => $date,
            'attendance' => $attendance,
            'count' => count($attendance)
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get attendance history for specific student
 */
function getStudentAttendance($pdo, $student_id, $start_date = '', $end_date = '') {
    try {
        if (empty($student_id)) {
            sendResponse(['success' => false, 'message' => 'Student ID required'], 400);
        }
        
        if (empty($start_date)) {
            $start_date = date('Y-m-d', strtotime('-30 days'));
        }
        if (empty($end_date)) {
            $end_date = date('Y-m-d');
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                attendance_date,
                check_in_time,
                check_out_time,
                hours_attended,
                status,
                notes
            FROM attendance 
            WHERE student_id = ? 
            AND attendance_date BETWEEN ? AND ?
            ORDER BY attendance_date DESC
        ");
        $stmt->execute([$student_id, $start_date, $end_date]);
        $attendance = $stmt->fetchAll();
        
        // Calculate summary statistics
        $stats = [
            'total_days' => count($attendance),
            'present_days' => 0,
            'partial_days' => 0,
            'absent_days' => 0,
            'total_hours' => 0,
            'average_hours' => 0
        ];
        
        foreach ($attendance as $record) {
            $stats[$record['status'] . '_days']++;
            $stats['total_hours'] += $record['hours_attended'];
        }
        
        if ($stats['total_days'] > 0) {
            $stats['average_hours'] = round($stats['total_hours'] / $stats['total_days'], 2);
        }
        
        sendResponse([
            'success' => true,
            'student_id' => $student_id,
            'period' => ['start' => $start_date, 'end' => $end_date],
            'attendance' => $attendance,
            'stats' => $stats
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching student attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get monthly attendance summary
 */
function getMonthlyAttendance($pdo, $month = '', $year = '') {
    try {
        if (empty($month)) {
            $month = date('m');
        }
        if (empty($year)) {
            $year = date('Y');
        }
        
        $start_date = "$year-$month-01";
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.course,
                COUNT(a.attendance_id) as total_days,
                SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN a.status = 'partial' THEN 1 ELSE 0 END) as partial_days,
                SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
                SUM(a.hours_attended) as total_hours,
                AVG(a.hours_attended) as average_hours
            FROM students s
            LEFT JOIN attendance a ON s.student_id = a.student_id 
                AND a.attendance_date BETWEEN ? AND ?
            WHERE s.status = 'active'
            GROUP BY s.student_id
            ORDER BY s.name
        ");
        $stmt->execute([$start_date, $end_date]);
        $monthly = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'month' => $month,
            'year' => $year,
            'period' => ['start' => $start_date, 'end' => $end_date],
            'attendance' => $monthly
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching monthly attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get attendance statistics
 */
function getAttendanceStats($pdo) {
    try {
        $today = date('Y-m-d');
        $this_month = date('Y-m');
        
        // Today's stats
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_students,
                SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_today,
                SUM(CASE WHEN a.status = 'partial' THEN 1 ELSE 0 END) as partial_today,
                SUM(CASE WHEN a.status = 'absent' OR a.status IS NULL THEN 1 ELSE 0 END) as absent_today
            FROM students s
            LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = ?
            WHERE s.status = 'active'
        ");
        $stmt->execute([$today]);
        $today_stats = $stmt->fetch();
        
        // This month's stats
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT s.student_id) as total_students,
                COUNT(DISTINCT CASE WHEN a.status = 'present' THEN s.student_id END) as students_with_present,
                AVG(CASE WHEN a.status != 'absent' THEN a.hours_attended END) as avg_daily_hours
            FROM students s
            LEFT JOIN attendance a ON s.student_id = a.student_id 
                AND DATE_FORMAT(a.attendance_date, '%Y-%m') = ?
            WHERE s.status = 'active'
        ");
        $stmt->execute([$this_month]);
        $month_stats = $stmt->fetch();
        
        sendResponse([
            'success' => true,
            'today' => $today_stats,
            'this_month' => $month_stats,
            'generated_at' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching stats: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update attendance record manually
 */
function updateAttendance($pdo, $data) {
    try {
        $required = ['student_id', 'date'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendResponse([
                'success' => false,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ], 400);
        }
        
        $student_id = $data['student_id'];
        $date = $data['date'];
        
        // Check if record exists
        $stmt = $pdo->prepare("
            SELECT attendance_id FROM attendance 
            WHERE student_id = ? AND attendance_date = ?
        ");
        $stmt->execute([$student_id, $date]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update existing record
            $updateFields = [];
            $updateValues = [];
            
            $allowedFields = ['check_in_time', 'check_out_time', 'hours_attended', 'status', 'notes'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $data[$field];
                }
            }
            
            if (!empty($updateFields)) {
                $updateValues[] = $existing['attendance_id'];
                
                $stmt = $pdo->prepare("
                    UPDATE attendance 
                    SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP 
                    WHERE attendance_id = ?
                ");
                $stmt->execute($updateValues);
            }
        } else {
            // Create new record
            $stmt = $pdo->prepare("
                INSERT INTO attendance 
                (student_id, attendance_date, check_in_time, check_out_time, hours_attended, status, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $student_id,
                $date,
                $data['check_in_time'] ?? null,
                $data['check_out_time'] ?? null,
                $data['hours_attended'] ?? 0,
                $data['status'] ?? 'absent',
                $data['notes'] ?? null
            ]);
        }
        
        logSystemEvent($pdo, 'attendance_update', $student_id, $data);
        
        sendResponse([
            'success' => true,
            'message' => 'Attendance updated successfully'
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error updating attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Mark all students as absent for a specific date
 */
function markStudentsAbsent($pdo, $date) {
    try {
        if (empty($date)) {
            $date = date('Y-m-d');
        }
        
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO attendance (student_id, attendance_date, status)
            SELECT student_id, ?, 'absent'
            FROM students 
            WHERE status = 'active'
        ");
        $stmt->execute([$date]);
        
        $affected = $stmt->rowCount();
        
        sendResponse([
            'success' => true,
            'message' => "Marked $affected students as absent for $date",
            'date' => $date,
            'affected_students' => $affected
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error marking students absent: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Generate test attendance data for prototype
 */
function generateTestAttendance($pdo, $data) {
    try {
        $date = $data['date'] ?? date('Y-m-d');
        $days_back = $data['days_back'] ?? 7;
        
        // Get all active students
        $stmt = $pdo->prepare("SELECT student_id FROM students WHERE status = 'active'");
        $stmt->execute();
        $students = $stmt->fetchAll();
        
        $generated = 0;
        
        for ($i = 0; $i < $days_back; $i++) {
            $test_date = date('Y-m-d', strtotime("$date -$i days"));
            
            foreach ($students as $student) {
                // Random attendance pattern
                $random = rand(1, 100);
                
                if ($random <= 75) { // 75% chance of present
                    $check_in = sprintf("%02d:%02d:00", rand(7, 9), rand(0, 59));
                    $check_out = sprintf("%02d:%02d:00", rand(15, 17), rand(0, 59));
                    $hours = rand(7, 9) + (rand(0, 59) / 60);
                    $status = $hours >= 8 ? 'present' : 'partial';
                } elseif ($random <= 85) { // 10% chance of partial
                    $check_in = sprintf("%02d:%02d:00", rand(7, 10), rand(0, 59));
                    $check_out = sprintf("%02d:%02d:00", rand(11, 14), rand(0, 59));
                    $hours = rand(3, 6) + (rand(0, 59) / 60);
                    $status = 'partial';
                } else { // 15% chance of absent
                    $check_in = null;
                    $check_out = null;
                    $hours = 0;
                    $status = 'absent';
                }
                
                $stmt = $pdo->prepare("
                    INSERT IGNORE INTO attendance 
                    (student_id, attendance_date, check_in_time, check_out_time, hours_attended, status) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $student['student_id'],
                    $test_date,
                    $check_in,
                    $check_out,
                    $hours,
                    $status
                ]);
                
                if ($stmt->rowCount() > 0) {
                    $generated++;
                }
            }
        }
        
        sendResponse([
            'success' => true,
            'message' => "Generated $generated test attendance records",
            'generated_records' => $generated,
            'period' => ['start' => date('Y-m-d', strtotime("$date -" . ($days_back - 1) . " days")), 'end' => $date]
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error generating test data: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Clear today's attendance data for testing
 */
function clearTodayAttendance($pdo) {
    try {
        $today = date('Y-m-d');
        
        $stmt = $pdo->prepare("DELETE FROM attendance WHERE attendance_date = ?");
        $stmt->execute([$today]);
        
        $deleted = $stmt->rowCount();
        
        sendResponse([
            'success' => true,
            'message' => "Cleared $deleted attendance records for today",
            'date' => $today,
            'deleted_records' => $deleted
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error clearing today\'s data: ' . $e->getMessage()
        ], 500);
    }
}
?>