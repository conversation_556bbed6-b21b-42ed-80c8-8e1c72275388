<?php
/**
 * COMPLETE Exam API - Handle ALL Exam Functions Including Email Warnings
 * FULLY IMPLEMENTED - PRODUCTION READY
 * Attendance System
 */

require_once 'config.php';

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'eligibility':
        getExamEligibility($pdo);
        break;
    case 'check_eligibility':
        checkStudentEligibility($pdo, $input);
        break;
    case 'setup_exam':
        setupExam($pdo, $input);
        break;
    case 'start_session':
        startExamSession($pdo, $input);
        break;
    case 'end_session':
        endExamSession($pdo);
        break;
    case 'record_attendance':
        recordExamAttendance($pdo, $input);
        break;
    case 'send_warnings':
        sendWarningEmailsFixed($pdo);
        break;
    case 'send_test_warning':
        sendTestWarningEmail($pdo, $input);
        break;
    case 'send_confirmations':
        sendConfirmationEmails($pdo);
        break;
    case 'get_session':
        getCurrentSession($pdo);
        break;
    case 'exam_stats':
        getExamStats($pdo);
        break;
    case 'exam_history':
        getExamHistory($pdo, $input['course'] ?? '');
        break;
    case 'record_access':
        recordExamAccessFromESP32($pdo, $input);
        break;
    default:
        sendResponse(['success' => false, 'message' => 'Invalid action'], 400);
}

/**
 * FIXED: Enhanced Student Eligibility Check - PROPERLY ENFORCES RULES
 */
function checkStudentEligibility($pdo, $data) {
    try {
        $fingerprint_id = $data['fingerprint_id'] ?? '';
        
        if (empty($fingerprint_id)) {
            error_log("❌ EXAM ACCESS: No fingerprint ID provided");
            sendResponse([
                'success' => false, 
                'eligible' => false,
                'message' => 'Fingerprint ID required', 
                'reason' => 'Invalid fingerprint'
            ], 400);
            return;
        }
        
        error_log("🔍 EXAM ACCESS CHECK: Fingerprint ID $fingerprint_id");
        
        // STEP 1: Find student by fingerprint
        $stmt = $pdo->prepare("
            SELECT student_id, name, email, course 
            FROM students 
            WHERE fingerprint_template = ? AND status = 'active'
        ");
        $stmt->execute([$fingerprint_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            error_log("❌ EXAM ACCESS DENIED: Fingerprint ID $fingerprint_id not found in database");
            sendResponse([
                'success' => false,
                'eligible' => false,
                'message' => 'Fingerprint not recognized',
                'reason' => 'Unregistered fingerprint'
            ], 404);
            return;
        }
        
        $student_id = $student['student_id'];
        $student_name = $student['name'];
        
        error_log("🎓 CHECKING ELIGIBILITY: Student '$student_name' (ID: $student_id)");
        
        // STEP 2: Check if exam session is active (optional for demo)
        $session = getCurrentExamSession($pdo);
        $course_name = 'Default Course'; // Default if no session
        if ($session && $session['active']) {
            $course_name = $session['course_name'];
            error_log("📋 Active exam session found: $course_name");
        } else {
            error_log("⚠️  No active exam session, but allowing access for demo");
        }
        
        // STEP 3: CRITICAL - Check detailed eligibility
        $eligibility = checkDetailedEligibility($pdo, $student_id);
        
        error_log("📊 DETAILED ELIGIBILITY RESULTS for '$student_name':");
        error_log("   👥 Attendance Eligible: " . ($eligibility['attendance_eligible'] ? 'YES' : 'NO'));
        error_log("   💰 Finance Eligible: " . ($eligibility['finance_eligible'] ? 'YES' : 'NO'));  
        error_log("   🎯 Overall Eligible: " . ($eligibility['overall_eligible'] ? 'YES' : 'NO'));
        error_log("   📊 Absent Days: " . $eligibility['absent_count'] . "/" . $eligibility['max_absences']);
        error_log("   💳 Payment: $" . $eligibility['amount_paid'] . "/$" . $eligibility['required_amount']);
        
        if ($eligibility['overall_eligible']) {
            // ✅ GRANT ACCESS
            error_log("✅ EXAM ACCESS GRANTED: '$student_name' meets all requirements");
            
            // Record successful access
            recordExamAccess($pdo, $student_id, $course_name, true, 'All requirements met');
            
            sendResponse([
                'success' => true,
                'eligible' => true,
                'message' => 'Access granted - All requirements met',
                'student_name' => $student_name,
                'student_id' => $student_id,
                'course' => $course_name,
                'reason' => 'Eligible for exam',
                'details' => [
                    'attendance_status' => 'Good (' . $eligibility['absent_count'] . ' absences)',
                    'payment_status' => 'Paid ($' . number_format($eligibility['amount_paid'], 2) . ')',
                    'exam_course' => $course_name
                ]
            ]);
            
        } else {
            // ❌ DENY ACCESS
            $denial_reason = $eligibility['denial_reason'];
            error_log("❌ EXAM ACCESS DENIED: '$student_name' - $denial_reason");
            
            // Record denied access
            recordExamAccess($pdo, $student_id, $course_name, false, $denial_reason);
            
            sendResponse([
                'success' => true,  // Request was successful, but access denied
                'eligible' => false,
                'message' => 'Access denied - Requirements not met',
                'student_name' => $student_name,
                'student_id' => $student_id,
                'reason' => $denial_reason,
                'details' => [
                    'attendance_status' => $eligibility['attendance_eligible'] ? 
                        'Good (' . $eligibility['absent_count'] . ' absences)' : 
                        'Poor (' . $eligibility['absent_count'] . '/' . $eligibility['max_absences'] . ' absences)',
                    'payment_status' => $eligibility['finance_eligible'] ? 
                        'Paid ($' . number_format($eligibility['amount_paid'], 2) . ')' : 
                        'Unpaid ($' . number_format($eligibility['amount_paid'], 2) . '/$' . number_format($eligibility['required_amount'], 2) . ')',
                    'required_attendance' => 'Maximum ' . $eligibility['max_absences'] . ' absences allowed',
                    'required_payment' => '$' . number_format($eligibility['required_amount'], 2) . ' required'
                ]
            ]);
        }
        
    } catch (Exception $e) {
        error_log("❌ EXAM ELIGIBILITY SYSTEM ERROR: " . $e->getMessage());
        sendResponse([
            'success' => false,
            'eligible' => false,
            'message' => 'System error during eligibility check',
            'reason' => 'System error - please contact admin'
        ], 500);
    }
}

/**
 * CRITICAL FUNCTION: Detailed eligibility check with PROPER LOGIC
 */
function checkDetailedEligibility($pdo, $student_id) {
    try {
        // Get system settings
        $max_absences = (int)getSetting($pdo, 'max_absences_per_month', 4);
        $attendance_percentage_required = (float)getSetting($pdo, 'attendance_percentage_required', 80.0);
        
        error_log("📋 ELIGIBILITY SETTINGS: Max absences=$max_absences, Min attendance=$attendance_percentage_required%");
        
        // Get attendance data for last 30 days
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_days,
                SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
                SUM(CASE WHEN status = 'partial' THEN 1 ELSE 0 END) as partial_days
            FROM attendance 
            WHERE student_id = ? 
            AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
        $stmt->execute([$student_id]);
        $attendance = $stmt->fetch();
        
        $total_days = (int)($attendance['total_days'] ?? 0);
        $present_days = (int)($attendance['present_days'] ?? 0);
        $absent_days = (int)($attendance['absent_days'] ?? 0);
        $partial_days = (int)($attendance['partial_days'] ?? 0);
        
        // Calculate attendance percentage (treat partial as 0.5 present)
        $effective_present = $present_days + ($partial_days * 0.5);
        $attendance_percentage = $total_days > 0 ? ($effective_present / $total_days) * 100 : 100;
        
        error_log("📊 ATTENDANCE DATA: Total=$total_days, Present=$present_days, Absent=$absent_days, Partial=$partial_days");
        error_log("📈 ATTENDANCE CALC: Effective present=$effective_present, Percentage=" . round($attendance_percentage, 1) . "%");
        
        // Get finance data
        $stmt = $pdo->prepare("
            SELECT required_amount, amount_paid, payment_status 
            FROM finance 
            WHERE student_id = ?
        ");
        $stmt->execute([$student_id]);
        $finance = $stmt->fetch();
        
        $required_amount = (float)($finance['required_amount'] ?? 100.0);
        $amount_paid = (float)($finance['amount_paid'] ?? 0.0);
        $payment_status = $finance['payment_status'] ?? 'unpaid';
        
        error_log("💰 FINANCE DATA: Required=$required_amount, Paid=$amount_paid, Status=$payment_status");
        
        // ATTENDANCE ELIGIBILITY CHECKS
        $attendance_eligible = true;
        $attendance_reasons = [];
        
        // Check 1: Maximum absences rule
        if ($absent_days >= $max_absences) {
            $attendance_eligible = false;
            $attendance_reasons[] = "Too many absences ($absent_days >= $max_absences limit)";
            error_log("❌ ATTENDANCE FAIL: Absences ($absent_days) >= limit ($max_absences)");
        }
        
        // Check 2: Minimum attendance percentage rule  
        if ($attendance_percentage < $attendance_percentage_required) {
            $attendance_eligible = false;
            $attendance_reasons[] = sprintf("Low attendance (%.1f%% < %.1f%% required)", 
                                          $attendance_percentage, $attendance_percentage_required);
            error_log("❌ ATTENDANCE FAIL: Percentage (" . round($attendance_percentage, 1) . "%) < required ($attendance_percentage_required%)");
        }
        
        if ($attendance_eligible) {
            error_log("✅ ATTENDANCE PASS: Student meets attendance requirements");
        }
        
        // FINANCE ELIGIBILITY CHECKS
        $finance_eligible = ($amount_paid >= $required_amount);
        $finance_reasons = [];
        
        if (!$finance_eligible) {
            $balance = $required_amount - $amount_paid;
            $finance_reasons[] = sprintf("Payment required ($%.2f outstanding)", $balance);
            error_log("❌ FINANCE FAIL: Outstanding balance $" . number_format($balance, 2));
        } else {
            error_log("✅ FINANCE PASS: Student has paid required amount");
        }
        
        // OVERALL ELIGIBILITY
        $overall_eligible = $attendance_eligible && $finance_eligible;
        
        // BUILD COMPREHENSIVE DENIAL REASON
        $all_reasons = array_merge($attendance_reasons, $finance_reasons);
        $denial_reason = !empty($all_reasons) ? implode(' and ', $all_reasons) : '';
        
        error_log("🎯 FINAL DECISION: Overall eligible = " . ($overall_eligible ? 'YES' : 'NO'));
        if (!$overall_eligible) {
            error_log("📝 DENIAL REASON: $denial_reason");
        }
        
        return [
            'attendance_eligible' => $attendance_eligible,
            'finance_eligible' => $finance_eligible,
            'overall_eligible' => $overall_eligible,
            'absent_count' => $absent_days,
            'total_days' => $total_days,
            'present_days' => $present_days,
            'partial_days' => $partial_days,
            'attendance_percentage' => round($attendance_percentage, 1),
            'amount_paid' => $amount_paid,
            'required_amount' => $required_amount,
            'balance' => max(0, $required_amount - $amount_paid),
            'max_absences' => $max_absences,
            'attendance_percentage_required' => $attendance_percentage_required,
            'denial_reason' => $denial_reason,
            'attendance_reasons' => $attendance_reasons,
            'finance_reasons' => $finance_reasons
        ];
        
    } catch (Exception $e) {
        error_log("❌ ERROR in checkDetailedEligibility: " . $e->getMessage());
        return [
            'attendance_eligible' => false,
            'finance_eligible' => false,
            'overall_eligible' => false,
            'denial_reason' => 'System error during eligibility check'
        ];
    }
}

/**
 * Get exam eligibility for all students (for web interface)
 */
function getExamEligibility($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                f.required_amount,
                f.amount_paid,
                f.payment_status,
                COALESCE(att.absent_count, 0) as absent_count,
                COALESCE(att.total_days, 0) as total_days,
                
                -- Attendance eligibility
                CASE 
                    WHEN COALESCE(att.absent_count, 0) < ? THEN 1
                    ELSE 0
                END as attendance_eligible,
                
                -- Finance eligibility  
                CASE 
                    WHEN f.amount_paid >= f.required_amount THEN 1
                    ELSE 0
                END as finance_eligible,
                
                -- Overall eligibility
                CASE 
                    WHEN f.amount_paid >= f.required_amount 
                    AND COALESCE(att.absent_count, 0) < ? THEN 1
                    ELSE 0
                END as overall_eligible,
                
                -- Get exam status if exists
                COALESCE(er.exam_status, 'absent') as exam_status,
                er.access_time,
                er.denial_reason
                
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            LEFT JOIN (
                SELECT 
                    student_id,
                    COUNT(*) as total_days,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
                FROM attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY student_id
            ) att ON s.student_id = att.student_id
            LEFT JOIN exam_records er ON s.student_id = er.student_id 
                AND er.exam_date = CURDATE()
            WHERE s.status = 'active'
            ORDER BY s.name
        ");
        
        $max_absences = getSetting($pdo, 'max_absences_per_month', 4);
        $stmt->execute([$max_absences, $max_absences]);
        $students = $stmt->fetchAll();
        
        // Process each student to add readable status
        foreach ($students as &$student) {
            $student['attendance_status'] = $student['attendance_eligible'] ? 'eligible' : 'ineligible';
            $student['payment_status'] = $student['finance_eligible'] ? 'paid' : 'unpaid';
            $student['eligible'] = (bool)$student['overall_eligible'];
            
            // Determine reason for ineligibility
            if (!$student['overall_eligible']) {
                if (!$student['attendance_eligible'] && !$student['finance_eligible']) {
                    $student['ineligible_reason'] = 'Poor attendance and payment required';
                } elseif (!$student['attendance_eligible']) {
                    $student['ineligible_reason'] = 'Too many absences (' . $student['absent_count'] . ')';
                } else {
                    $balance = $student['required_amount'] - $student['amount_paid'];
                    $student['ineligible_reason'] = 'Payment required ($' . number_format($balance, 2) . ')';
                }
            } else {
                $student['ineligible_reason'] = null;
            }
        }
        
        sendResponse([
            'success' => true,
            'data' => $students,
            'settings' => [
                'max_absences' => $max_absences,
                'exam_date' => date('Y-m-d')
            ]
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching exam eligibility: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Setup exam configuration
 */
function setupExam($pdo, $data) {
    try {
        $required = ['course_name', 'exam_date'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendResponse([
                'success' => false,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ], 400);
        }
        
        $course_name = $data['course_name'];
        $exam_date = $data['exam_date'];
        
        // Validate date format
        if (!DateTime::createFromFormat('Y-m-d', $exam_date)) {
            sendResponse(['success' => false, 'message' => 'Invalid date format'], 400);
        }
        
        // Store exam configuration in settings
        updateSetting($pdo, 'current_exam_course', $course_name);
        updateSetting($pdo, 'current_exam_date', $exam_date);
        updateSetting($pdo, 'exam_session_active', '0');
        
        // Initialize exam records for all students
        initializeExamRecords($pdo, $course_name, $exam_date);
        
        logSystemEvent($pdo, 'exam_setup', null, [
            'course_name' => $course_name,
            'exam_date' => $exam_date
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Exam setup completed',
            'course_name' => $course_name,
            'exam_date' => $exam_date
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error setting up exam: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Start exam session
 */
function startExamSession($pdo, $data) {
    try {
        $course = $data['course'] ?? getSetting($pdo, 'current_exam_course', 'Default Course');
        
        // Check if session already active
        $current_session = getCurrentExamSession($pdo);
        if ($current_session) {
            sendResponse([
                'success' => false,
                'message' => 'Exam session already active for ' . $current_session['course_name']
            ], 409);
        }
        
        // Start session
        updateSetting($pdo, 'exam_session_active', '1');
        updateSetting($pdo, 'exam_session_start_time', date('Y-m-d H:i:s'));
        updateSetting($pdo, 'current_exam_course', $course);
        
        logSystemEvent($pdo, 'exam_start', null, [
            'course' => $course,
            'start_time' => date('Y-m-d H:i:s')
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Exam session started',
            'course' => $course,
            'start_time' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error starting exam session: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: End exam session
 */
function endExamSession($pdo) {
    try {
        $session = getCurrentExamSession($pdo);
        
        if (!$session) {
            sendResponse(['success' => false, 'message' => 'No active exam session'], 400);
        }
        
        // End session
        updateSetting($pdo, 'exam_session_active', '0');
        updateSetting($pdo, 'exam_session_end_time', date('Y-m-d H:i:s'));
        
        // Send confirmation emails to attendees
        $attendees = sendConfirmationEmailsToAttendees($pdo, $session['course_name']);
        
        logSystemEvent($pdo, 'exam_end', null, [
            'course' => $session['course_name'],
            'end_time' => date('Y-m-d H:i:s'),
            'attendees' => $attendees
        ]);
        
        sendResponse([
            'success' => true,
            'message' => 'Exam session ended. Confirmation emails sent.',
            'course' => $session['course_name'],
            'end_time' => date('Y-m-d H:i:s'),
            'attendees_notified' => $attendees
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error ending exam session: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Record exam attendance
 */
function recordExamAttendance($pdo, $data) {
    try {
        $required = ['student_id', 'course_name'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendResponse([
                'success' => false,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ], 400);
        }
        
        $success = recordExamAccess($pdo, $data['student_id'], $data['course_name'], true, 'Manual attendance record');
        
        if ($success) {
            sendResponse([
                'success' => true,
                'message' => 'Exam attendance recorded'
            ]);
        } else {
            sendResponse([
                'success' => false,
                'message' => 'Failed to record exam attendance'
            ], 500);
        }
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error recording exam attendance: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Send warning emails to ineligible students - REAL EMAIL FUNCTIONALITY
 */
function sendWarningEmailsFixed($pdo) {
    try {
        $course = getSetting($pdo, 'current_exam_course', 'Default Course');
        $exam_date = getSetting($pdo, 'current_exam_date', date('Y-m-d'));
        
        error_log("📧 STARTING WARNING EMAIL PROCESS: Course=$course, Date=$exam_date");
        
        // Get ineligible students with detailed information
        $stmt = $pdo->prepare("
            SELECT 
                s.student_id,
                s.name,
                s.email,
                s.course,
                f.required_amount,
                f.amount_paid,
                COALESCE(att.absent_count, 0) as absent_count,
                COALESCE(att.total_days, 0) as total_days
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            LEFT JOIN (
                SELECT 
                    student_id,
                    COUNT(*) as total_days,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
                FROM attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY student_id
            ) att ON s.student_id = att.student_id
            WHERE s.status = 'active'
            AND (
                f.amount_paid < f.required_amount 
                OR COALESCE(att.absent_count, 0) >= ?
            )
            ORDER BY s.name
        ");
        
        $max_absences = getSetting($pdo, 'max_absences_per_month', 4);
        $stmt->execute([$max_absences]);
        $ineligible = $stmt->fetchAll();
        
        error_log("📊 Found " . count($ineligible) . " ineligible students for warning emails");
        
        if (count($ineligible) == 0) {
            sendResponse([
                'success' => true,
                'message' => 'No ineligible students found - all students are eligible!',
                'count' => 0,
                'total_ineligible' => 0
            ]);
            return;
        }
        
        $sent = 0;
        $failed = 0;
        $email_details = [];
        
        foreach ($ineligible as $student) {
            $reasons = [];
            
            // Check attendance issues
            if ($student['absent_count'] >= $max_absences) {
                $reasons[] = "Poor attendance ({$student['absent_count']} absences, maximum $max_absences allowed)";
            }
            
            // Check payment issues
            if ($student['amount_paid'] < $student['required_amount']) {
                $balance = $student['required_amount'] - $student['amount_paid'];
                $reasons[] = "Outstanding payment ($" . number_format($balance, 2) . " required)";
            }
            
            $reasons_text = implode(' and ', $reasons);
            
            // Create email content
            $subject = "⚠️ Exam Eligibility Warning - $course";
            
            // Use HTML email template if available
            if (function_exists('generateWarningEmailHTML')) {
                $message = generateWarningEmailHTML($student['name'], $course, $exam_date, $reasons);
            } else {
                // Fallback to plain text
                $message = "Dear {$student['name']},\n\n";
                $message .= "You are currently NOT ELIGIBLE to attend the upcoming exam:\n\n";
                $message .= "Course: $course\n";
                $message .= "Exam Date: $exam_date\n\n";
                $message .= "Reason(s) for ineligibility:\n";
                $message .= "• $reasons_text\n\n";
                $message .= "ACTION REQUIRED:\n";
                $message .= "Please contact the administration office immediately to resolve these issues before the exam date.\n\n";
                $message .= "Contact Information:\n";
                $message .= "Email: <EMAIL>\n";
                $message .= "Office Hours: 9:00 AM - 5:00 PM, Monday - Friday\n\n";
                $message .= "Best regards,\n";
                $message .= "Attendance Management System\n";
                $message .= "University Administration";
            }
            
            // Send the email
            error_log("📧 Sending warning email to: {$student['name']} ({$student['email']}) - Reason: $reasons_text");
            
            if (sendEmail($student['email'], $subject, $message, 'warning')) {
                $sent++;
                $email_details[] = [
                    'student' => $student['name'],
                    'email' => $student['email'],
                    'reason' => $reasons_text,
                    'status' => 'sent'
                ];
                error_log("✅ WARNING EMAIL SENT: {$student['name']} ({$student['email']})");
            } else {
                $failed++;
                $email_details[] = [
                    'student' => $student['name'],
                    'email' => $student['email'],
                    'reason' => $reasons_text,
                    'status' => 'failed'
                ];
                error_log("❌ WARNING EMAIL FAILED: {$student['name']} ({$student['email']})");
            }
        }
        
        // Log the email campaign
        logSystemEvent($pdo, 'warning_emails_campaign', null, [
            'course' => $course,
            'exam_date' => $exam_date,
            'total_ineligible' => count($ineligible),
            'emails_sent' => $sent,
            'emails_failed' => $failed,
            'email_mode' => EMAIL_MODE,
            'details' => $email_details
        ]);
        
        error_log("📧 WARNING EMAIL CAMPAIGN COMPLETE: Sent=$sent, Failed=$failed, Total=" . count($ineligible));
        
        sendResponse([
            'success' => true,
            'message' => "Warning email campaign completed. Sent $sent emails, $failed failed.",
            'count' => $sent,
            'failed' => $failed,
            'total_ineligible' => count($ineligible),
            'email_mode' => EMAIL_MODE,
            'details' => $email_details
        ]);
        
    } catch (Exception $e) {
        error_log("❌ ERROR in warning email campaign: " . $e->getMessage());
        sendResponse([
            'success' => false,
            'message' => 'Error sending warning emails: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Send test warning email
 */
function sendTestWarningEmail($pdo, $data) {
    try {
        $testEmail = $data['test_email'] ?? '';
        
        if (empty($testEmail) || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            sendResponse(['success' => false, 'message' => 'Valid test email required'], 400);
            return;
        }
        
        $course = getSetting($pdo, 'current_exam_course', 'Sample Course');
        $examDate = getSetting($pdo, 'current_exam_date', date('Y-m-d'));
        
        error_log("📧 SENDING TEST WARNING EMAIL to: $testEmail");
        
        // Create test reasons
        $reasons = [
            'This is a test warning email',
            'Poor attendance (4 absences)',
            'Payment required ($100.00 outstanding)'
        ];
        
        $subject = "🧪 TEST - Exam Eligibility Warning - $course";
        
        // Use HTML template if available
        if (function_exists('generateWarningEmailHTML')) {
            $message = generateWarningEmailHTML('Test Student', $course, $examDate, $reasons);
        } else {
            // Fallback to plain text
            $message = "🧪 TEST EMAIL - Exam Eligibility Warning\n\n";
            $message .= "Dear Test Student,\n\n";
            $message .= "This is a TEST warning email to verify the email system is working correctly.\n\n";
            $message .= "Course: $course\n";
            $message .= "Exam Date: $examDate\n\n";
            $message .= "Test reasons for ineligibility:\n";
            foreach ($reasons as $reason) {
                $message .= "• $reason\n";
            }
            $message .= "\nIf you received this email, the email notification system is working correctly!\n\n";
            $message .= "Email Mode: " . EMAIL_MODE . "\n";
            $message .= "Timestamp: " . date('Y-m-d H:i:s') . "\n\n";
            $message .= "Best regards,\n";
            $message .= "Attendance Management System (Testing Mode)";
        }
        
        // Send the test email
        $emailSent = sendEmail($testEmail, $subject, $message, 'test_warning');
        
        if ($emailSent) {
            // Log the test
            logSystemEvent($pdo, 'test_warning_email', null, [
                'test_email' => $testEmail,
                'course' => $course,
                'exam_date' => $examDate,
                'email_mode' => EMAIL_MODE,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
            error_log("✅ TEST WARNING EMAIL SENT successfully to: $testEmail");
            
            sendResponse([
                'success' => true,
                'message' => "Test warning email sent successfully to $testEmail",
                'test_email' => $testEmail,
                'email_mode' => EMAIL_MODE,
                'course' => $course,
                'exam_date' => $examDate
            ]);
        } else {
            error_log("❌ TEST WARNING EMAIL FAILED to: $testEmail");
            sendResponse([
                'success' => false,
                'message' => 'Failed to send test email. Check email configuration.',
                'test_email' => $testEmail,
                'email_mode' => EMAIL_MODE
            ], 500);
        }
        
    } catch (Exception $e) {
        error_log("❌ ERROR sending test warning email: " . $e->getMessage());
        sendResponse([
            'success' => false,
            'message' => 'Error sending test email: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Send confirmation emails to exam attendees
 */
function sendConfirmationEmails($pdo) {
    try {
        $course = getSetting($pdo, 'current_exam_course', 'Default Course');
        
        if (empty($course)) {
            sendResponse(['success' => false, 'message' => 'No exam configured'], 400);
        }
        
        $sent = sendConfirmationEmailsToAttendees($pdo, $course);
        
        sendResponse([
            'success' => true,
            'message' => "Confirmation emails sent to $sent attendees",
            'count' => $sent,
            'course' => $course
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error sending confirmation emails: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Get current exam session status
 */
function getCurrentSession($pdo) {
    try {
        $session = getCurrentExamSession($pdo);
        
        if ($session) {
            // Get attendance count
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as attendee_count 
                FROM exam_records 
                WHERE course_name = ? AND exam_date = CURDATE() AND exam_status = 'present'
            ");
            $stmt->execute([$session['course_name']]);
            $result = $stmt->fetch();
            $session['attendee_count'] = $result['attendee_count'];
            
            // Get eligibility stats
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(*) as total_students,
                    SUM(overall_eligible) as eligible_count,
                    COUNT(*) - SUM(overall_eligible) as ineligible_count
                FROM exam_records 
                WHERE course_name = ? AND exam_date = CURDATE()
            ");
            $stmt->execute([$session['course_name']]);
            $stats = $stmt->fetch();
            $session['eligibility_stats'] = $stats;
        }
        
        sendResponse([
            'success' => true,
            'session' => $session
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching session: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Get exam statistics
 */
function getExamStats($pdo) {
    try {
        $course = getSetting($pdo, 'current_exam_course', 'Default Course');
        $exam_date = getSetting($pdo, 'current_exam_date', date('Y-m-d'));
        
        // Overall eligibility stats
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_students,
                SUM(CASE WHEN f.amount_paid >= f.required_amount 
                    AND COALESCE(att.absent_count, 0) < ? THEN 1 ELSE 0 END) as eligible_count,
                SUM(CASE WHEN f.amount_paid < f.required_amount 
                    OR COALESCE(att.absent_count, 0) >= ? THEN 1 ELSE 0 END) as ineligible_count
            FROM students s
            LEFT JOIN finance f ON s.student_id = f.student_id
            LEFT JOIN (
                SELECT 
                    student_id,
                    SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
                FROM attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY student_id
            ) att ON s.student_id = att.student_id
            WHERE s.status = 'active'
        ");
        
        $max_absences = getSetting($pdo, 'max_absences_per_month', 4);
        $stmt->execute([$max_absences, $max_absences]);
        $eligibility = $stmt->fetch();
        
        // Exam attendance stats
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_records,
                SUM(CASE WHEN exam_status = 'present' THEN 1 ELSE 0 END) as attended_count,
                SUM(CASE WHEN exam_status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM exam_records 
            WHERE exam_date = ?
        ");
        $stmt->execute([$exam_date]);
        $attendance = $stmt->fetch();
        
        sendResponse([
            'success' => true,
            'eligibility' => $eligibility,
            'attendance' => $attendance,
            'exam_info' => [
                'course' => $course,
                'date' => $exam_date,
                'session_active' => (bool)getSetting($pdo, 'exam_session_active', '0')
            ]
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching exam stats: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Get exam history
 */
function getExamHistory($pdo, $course = '') {
    try {
        $where = '';
        $params = [];
        
        if (!empty($course)) {
            $where = 'WHERE course_name = ?';
            $params[] = $course;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                course_name,
                exam_date,
                COUNT(*) as total_students,
                SUM(CASE WHEN exam_status = 'present' THEN 1 ELSE 0 END) as attended,
                SUM(CASE WHEN overall_eligible = 1 THEN 1 ELSE 0 END) as eligible,
                SUM(CASE WHEN overall_eligible = 0 THEN 1 ELSE 0 END) as ineligible,
                MIN(created_at) as first_access,
                MAX(updated_at) as last_access
            FROM exam_records 
            $where
            GROUP BY course_name, exam_date
            ORDER BY exam_date DESC, course_name
        ");
        $stmt->execute($params);
        $history = $stmt->fetchAll();
        
        sendResponse([
            'success' => true,
            'history' => $history,
            'total_exams' => count($history)
        ]);
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error fetching exam history: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * COMPLETE: Handle exam access recording from ESP32
 */
function recordExamAccessFromESP32($pdo, $data) {
    try {
        $fingerprint_id = $data['fingerprint_id'] ?? '';
        $access_granted = $data['access_granted'] ?? false;
        $reason = $data['reason'] ?? '';
        
        if (empty($fingerprint_id)) {
            sendResponse(['success' => false, 'message' => 'Fingerprint ID required'], 400);
            return;
        }
        
        // Find student
        $stmt = $pdo->prepare("
            SELECT student_id, name FROM students 
            WHERE fingerprint_template = ? AND status = 'active'
        ");
        $stmt->execute([$fingerprint_id]);
        $student = $stmt->fetch();
        
        if (!$student) {
            sendResponse(['success' => false, 'message' => 'Student not found'], 404);
            return;
        }
        
        // Get current exam session
        $session = getCurrentExamSession($pdo);
        $course_name = $session['course_name'] ?? 'Unknown Course';
        
        // Record the access
        $success = recordExamAccess($pdo, $student['student_id'], $course_name, $access_granted, $reason);
        
        if ($success) {
            sendResponse([
                'success' => true,
                'message' => 'Exam access recorded',
                'student_name' => $student['name'],
                'access_granted' => $access_granted
            ]);
        } else {
            sendResponse([
                'success' => false,
                'message' => 'Failed to record exam access'
            ], 500);
        }
        
    } catch (Exception $e) {
        sendResponse([
            'success' => false,
            'message' => 'Error recording exam access: ' . $e->getMessage()
        ], 500);
    }
}

// ==========================================
// HELPER FUNCTIONS
// ==========================================

/**
 * Record exam access attempt with detailed logging
 */
function recordExamAccess($pdo, $student_id, $course_name, $granted, $reason) {
    try {
        $eligibility = checkDetailedEligibility($pdo, $student_id);
        
        $stmt = $pdo->prepare("
            INSERT INTO exam_records 
            (student_id, course_name, exam_date, exam_time, 
             attendance_eligible, finance_eligible, overall_eligible, 
             exam_status, access_time, denial_reason, attendance_percentage,
             absent_count, amount_paid, required_amount) 
            VALUES (?, ?, CURDATE(), CURTIME(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            exam_time = CURTIME(),
            exam_status = VALUES(exam_status),
            access_time = VALUES(access_time),
            denial_reason = VALUES(denial_reason),
            updated_at = CURRENT_TIMESTAMP
        ");
        
        $stmt->execute([
            $student_id,
            $course_name,
            $eligibility['attendance_eligible'] ? 1 : 0,
            $eligibility['finance_eligible'] ? 1 : 0,
            $eligibility['overall_eligible'] ? 1 : 0,
            $granted ? 'present' : 'absent',
            $granted ? date('Y-m-d H:i:s') : null,
            $reason,
            $eligibility['attendance_percentage'] ?? null,
            $eligibility['absent_count'] ?? null,
            $eligibility['amount_paid'] ?? null,
            $eligibility['required_amount'] ?? null
        ]);
        
        // Log to system
        logSystemEvent($pdo, 'exam_access', $student_id, [
            'course' => $course_name,
            'granted' => $granted,
            'reason' => $reason,
            'eligibility' => $eligibility
        ]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error recording exam access: " . $e->getMessage());
        return false;
    }
}

/**
 * Get current exam session information
 */
function getCurrentExamSession($pdo) {
    $active = getSetting($pdo, 'exam_session_active', '0');
    
    if ($active === '1') {
        return [
            'active' => true,
            'course_name' => getSetting($pdo, 'current_exam_course', 'Default Course'),
            'start_time' => getSetting($pdo, 'exam_session_start_time', ''),
            'exam_date' => getSetting($pdo, 'current_exam_date', date('Y-m-d'))
        ];
    }
    
    return null;
}

/**
 * Initialize exam records for all students
 */
function initializeExamRecords($pdo, $course_name, $exam_date) {
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO exam_records 
        (student_id, course_name, exam_date, attendance_eligible, finance_eligible, overall_eligible)
        SELECT 
            s.student_id,
            ?,
            ?,
            CASE WHEN COALESCE(att.absent_count, 0) < ? THEN 1 ELSE 0 END,
            CASE WHEN f.amount_paid >= f.required_amount THEN 1 ELSE 0 END,
            CASE WHEN f.amount_paid >= f.required_amount 
                AND COALESCE(att.absent_count, 0) < ? THEN 1 ELSE 0 END
        FROM students s
        LEFT JOIN finance f ON s.student_id = f.student_id
        LEFT JOIN (
            SELECT 
                student_id,
                SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM attendance 
            WHERE attendance_date >= DATE_SUB(?, INTERVAL 30 DAY)
            GROUP BY student_id
        ) att ON s.student_id = att.student_id
        WHERE s.status = 'active'
    ");
    
    $max_absences = getSetting($pdo, 'max_absences_per_month', 4);
    $stmt->execute([$course_name, $exam_date, $max_absences, $max_absences, $exam_date]);
}

/**
 * Send confirmation emails to attendees
 */
function sendConfirmationEmailsToAttendees($pdo, $course_name) {
    $stmt = $pdo->prepare("
        SELECT s.name, s.email, er.access_time
        FROM exam_records er
        JOIN students s ON er.student_id = s.student_id
        WHERE er.course_name = ? 
        AND er.exam_date = CURDATE() 
        AND er.exam_status = 'present'
    ");
    $stmt->execute([$course_name]);
    $attendees = $stmt->fetchAll();
    
    $sent = 0;
    foreach ($attendees as $attendee) {
        $subject = "✅ Exam Attendance Confirmation - $course_name";
        
        if (function_exists('generateConfirmationEmailHTML')) {
            $message = generateConfirmationEmailHTML(
                $attendee['name'], 
                $course_name, 
                date('Y-m-d'), 
                $attendee['access_time']
            );
        } else {
            $message = "Dear {$attendee['name']},\n\n";
            $message .= "This email confirms your attendance at the exam for $course_name on " . date('Y-m-d') . " at {$attendee['access_time']}.\n\n";
            $message .= "Your exam attendance has been successfully recorded in our system.\n\n";
            $message .= "Best regards,\n";
            $message .= "Attendance Management System";
        }
        
        if (sendEmail($attendee['email'], $subject, $message, 'confirmation')) {
            $sent++;
        }
    }
    
    return $sent;
}
?>