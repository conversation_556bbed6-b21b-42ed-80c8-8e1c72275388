#!/bin/bash
# File: setup.sh
# Dual-Authentication Smart Attendance System Setup Script

echo "======================================"
echo "INES Attendance System Setup"
echo "======================================"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "Please do not run this script as root"
    exit 1
fi

# Check system requirements
echo "Checking system requirements..."

# Check for PHP
if ! command -v php &> /dev/null; then
    echo "PHP is not installed. Please install PHP 7.4 or higher."
    exit 1
fi

# Check for MySQL
if ! command -v mysql &> /dev/null; then
    echo "MySQL is not installed. Please install MySQL 5.7 or higher."
    exit 1
fi

# Check for Apache/Nginx
if ! command -v apache2 &> /dev/null && ! command -v nginx &> /dev/null; then
    echo "Web server not found. Please install Apache or Nginx."
    exit 1
fi

echo "System requirements met!"

# Create necessary directories
echo "Creating directory structure..."
mkdir -p logs
mkdir -p uploads/{students,documents}
mkdir -p backups

# Set permissions
echo "Setting permissions..."
chmod 755 backend/
chmod 755 frontend/
chmod 777 logs/
chmod 777 uploads/
chmod 777 backups/

# Database setup
echo "Setting up database..."
read -p "Enter MySQL root password: " -s mysql_password
echo

# Create database
mysql -u root -p$mysql_password << EOF
CREATE DATABASE IF NOT EXISTS attendance_system;
CREATE USER IF NOT EXISTS 'attendance_user'@'localhost' IDENTIFIED BY 'attendance_pass_2024';
GRANT ALL PRIVILEGES ON attendance_system.* TO 'attendance_user'@'localhost';
FLUSH PRIVILEGES;
EOF

if [ $? -eq 0 ]; then
    echo "Database created successfully!"
else
    echo "Database creation failed!"
    exit 1
fi

# Import database schema
echo "Importing database schema..."
mysql -u root -p$mysql_password attendance_system < backend/sql/attendance_system.sql

if [ $? -eq 0 ]; then
    echo "Database schema imported successfully!"
else
    echo "Database schema import failed!"
    exit 1
fi

# Update database configuration
echo "Updating database configuration..."
sed -i "s/private \$username = 'root';/private \$username = 'attendance_user';/" backend/config/database.php
sed -i "s/private \$password = '';/private \$password = 'attendance_pass_2024';/" backend/config/database.php

# Install Composer dependencies (if composer.json exists)
if [ -f "composer.json" ]; then
    echo "Installing PHP dependencies..."
    if command -v composer &> /dev/null; then
        composer install
    else
        echo "Composer not found. Please install composer and run 'composer install'"
    fi
fi

# Create Apache virtual host configuration
echo "Creating Apache virtual host..."
sudo tee /etc/apache2/sites-available/attendance-system.conf > /dev/null << EOF
<VirtualHost *:80>
    ServerName attendance-system.local
    DocumentRoot $(pwd)
    
    <Directory $(pwd)>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/attendance-system_error.log
    CustomLog \${APACHE_LOG_DIR}/attendance-system_access.log combined
</VirtualHost>
EOF

# Enable the site
sudo a2ensite attendance-system.conf
sudo a2enmod rewrite
sudo systemctl reload apache2

# Add to hosts file
echo "127.0.0.1 attendance-system.local" | sudo tee -a /etc/hosts

# Create environment file
echo "Creating environment configuration..."
cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_NAME=attendance_system
DB_USER=attendance_user
DB_PASS=attendance_pass_2024

# Application Configuration
APP_NAME="INES Attendance System"
APP_URL=http://attendance-system.local
APP_ENV=production
APP_DEBUG=false

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_FROM=<EMAIL>

# System Configuration
ATTENDANCE_THRESHOLD=80
SESSION_TIMEOUT=3600
TIMEZONE=Africa/Kigali
EOF

# Create log rotation configuration
echo "Setting up log rotation..."
sudo tee /etc/logrotate.d/attendance-system > /dev/null << EOF
$(pwd)/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    sharedscripts
    postrotate
        /bin/systemctl reload apache2 > /dev/null 2>&1 || true
    endscript
}
EOF

# Create systemd service for hardware monitoring (optional)
echo "Creating hardware monitoring service..."
sudo tee /etc/systemd/system/attendance-hardware.service > /dev/null << EOF
[Unit]
Description=Attendance System Hardware Monitor
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/php $(pwd)/backend/services/hardware_monitor.php
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable the service
sudo systemctl daemon-reload
sudo systemctl enable attendance-hardware.service

# Create backup script
echo "Creating backup script..."
cat > backup.sh << 'EOF'
#!/bin/bash
# Backup script for attendance system

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="attendance_system"
DB_USER="attendance_user"
DB_PASS="attendance_pass_2024"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Database backup
echo "Backing up database..."
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# Files backup
echo "Backing up files..."
tar -czf $BACKUP_DIR/files_$DATE.tar.gz uploads/ logs/ .env

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x backup.sh

# Create maintenance script
echo "Creating maintenance script..."
cat > maintenance.sh << 'EOF'
#!/bin/bash
# Maintenance script for attendance system

echo "Starting maintenance tasks..."

# Clear old logs
find logs/ -name "*.log" -mtime +7 -delete

# Clear old sessions
find /tmp -name "sess_*" -mtime +1 -delete 2>/dev/null || true

# Optimize database
mysql -u attendance_user -pattendance_pass_2024 attendance_system << SQL
OPTIMIZE TABLE students, attendance_records, exam_attendance, notifications, audit_logs;
SQL

# Clear cache (if any)
rm -rf cache/* 2>/dev/null || true

echo "Maintenance completed!"
EOF

chmod +x maintenance.sh

# Setup cron jobs
echo "Setting up cron jobs..."
(crontab -l 2>/dev/null; echo "0 2 * * * $(pwd)/backup.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 3 * * 0 $(pwd)/maintenance.sh") | crontab -

# Final setup steps
echo "Finalizing setup..."

# Test database connection
php -r "
require_once 'backend/config/database.php';
try {
    \$db = new Database();
    \$conn = \$db->getConnection();
    echo 'Database connection successful!\n';
} catch (Exception \$e) {
    echo 'Database connection failed: ' . \$e->getMessage() . '\n';
    exit(1);
}
"

echo "======================================"
echo "Setup completed successfully!"
echo "======================================"
echo
echo "Next steps:"
echo "1. Access the system at: http://attendance-system.local"
echo "2. Login with default credentials:"
echo "   - Admin: <EMAIL> / admin123"
echo "   - Department: <EMAIL> / admin123"
echo "   - Accountant: <EMAIL> / admin123"
echo
echo "3. Configure email settings in .env file"
echo "4. Upload and program ESP32 devices with Arduino code"
echo "5. Update WiFi credentials in Arduino code"
echo
echo "Important files:"
echo "- Configuration: .env"
echo "- Logs: logs/"
echo "- Uploads: uploads/"
echo "- Backups: backups/"
echo
echo "Commands:"
echo "- Backup: ./backup.sh"
echo "- Maintenance: ./maintenance.sh"
echo "- View logs: tail -f logs/system.log"
echo
echo "For support, check the documentation in docs/"
echo "======================================"